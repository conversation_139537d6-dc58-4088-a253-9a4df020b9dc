<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>创建小组 - 打卡小程序</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body { overflow: hidden; }
        ::-webkit-scrollbar { display: none; }
        .gradient-bg { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .card-shadow { box-shadow: 0 4px 20px rgba(0,0,0,0.08); }
    </style>
</head>
<body class="bg-gray-50">
    <!-- 手机模拟器外框 -->
    <div class="mx-auto bg-black rounded-3xl p-2 shadow-2xl" style="width: 375px; height: 812px;">
        <div class="w-full h-full bg-white rounded-2xl overflow-hidden relative">
            <!-- 状态栏 -->
            <div class="flex justify-between items-center px-6 pt-3 pb-1 bg-transparent text-black text-sm font-medium">
                <span>9:41</span>
                <div class="flex items-center space-x-1">
                    <i class="fas fa-signal text-xs"></i>
                    <i class="fas fa-wifi text-xs"></i>
                    <i class="fas fa-battery-three-quarters text-xs"></i>
                </div>
            </div>

            <!-- 导航栏 -->
            <div class="flex items-center justify-between px-6 py-4 bg-transparent">
                <button class="w-8 h-8 flex items-center justify-center">
                    <i class="fas fa-arrow-left text-gray-600"></i>
                </button>
                <h1 class="text-lg font-semibold text-gray-800">创建小组</h1>
                <div class="w-8"></div>
            </div>

            <!-- 主要内容区域 -->
            <div class="px-6 pb-20 h-full overflow-y-auto">
                <!-- 小组图标选择 -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-3">选择小组图标</label>
                    <div class="grid grid-cols-5 gap-3">
                        <div class="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center border-2 border-blue-500">
                            <i class="fas fa-running text-blue-500 text-lg"></i>
                        </div>
                        <div class="w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center border-2 border-transparent">
                            <i class="fas fa-book text-purple-500 text-lg"></i>
                        </div>
                        <div class="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center border-2 border-transparent">
                            <i class="fas fa-dumbbell text-green-500 text-lg"></i>
                        </div>
                        <div class="w-12 h-12 bg-yellow-100 rounded-xl flex items-center justify-center border-2 border-transparent">
                            <i class="fas fa-music text-yellow-500 text-lg"></i>
                        </div>
                        <div class="w-12 h-12 bg-red-100 rounded-xl flex items-center justify-center border-2 border-transparent">
                            <i class="fas fa-paint-brush text-red-500 text-lg"></i>
                        </div>
                        <div class="w-12 h-12 bg-indigo-100 rounded-xl flex items-center justify-center border-2 border-transparent">
                            <i class="fas fa-code text-indigo-500 text-lg"></i>
                        </div>
                        <div class="w-12 h-12 bg-pink-100 rounded-xl flex items-center justify-center border-2 border-transparent">
                            <i class="fas fa-heart text-pink-500 text-lg"></i>
                        </div>
                        <div class="w-12 h-12 bg-teal-100 rounded-xl flex items-center justify-center border-2 border-transparent">
                            <i class="fas fa-leaf text-teal-500 text-lg"></i>
                        </div>
                        <div class="w-12 h-12 bg-orange-100 rounded-xl flex items-center justify-center border-2 border-transparent">
                            <i class="fas fa-camera text-orange-500 text-lg"></i>
                        </div>
                        <div class="w-12 h-12 bg-gray-100 rounded-xl flex items-center justify-center border-2 border-transparent">
                            <i class="fas fa-plus text-gray-500 text-lg"></i>
                        </div>
                    </div>
                </div>

                <!-- 小组名称 -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-3">小组名称 *</label>
                    <input 
                        type="text" 
                        placeholder="请输入小组名称"
                        class="w-full p-4 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        value="晨跑俱乐部"
                    >
                </div>

                <!-- 小组描述 -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-3">小组描述</label>
                    <textarea 
                        placeholder="介绍一下这个小组的目标和规则..."
                        class="w-full h-24 p-4 border border-gray-200 rounded-xl resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >每天早上6点一起晨跑，养成健康生活习惯，欢迎大家积极参与！</textarea>
                    <div class="text-right text-xs text-gray-400 mt-1">45/200</div>
                </div>

                <!-- 打卡设置 -->
                <div class="bg-white rounded-xl p-5 mb-6 card-shadow">
                    <h3 class="font-semibold text-gray-800 mb-4">打卡设置</h3>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-3">每月最低打卡次数</label>
                        <div class="flex items-center space-x-3">
                            <button class="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center">
                                <i class="fas fa-minus text-gray-600"></i>
                            </button>
                            <div class="flex-1 text-center">
                                <span class="text-2xl font-bold text-gray-800">22</span>
                                <span class="text-sm text-gray-500 ml-1">次</span>
                            </div>
                            <button class="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center">
                                <i class="fas fa-plus text-gray-600"></i>
                            </button>
                        </div>
                    </div>

                    <div class="flex items-center justify-between">
                        <div>
                            <span class="font-medium text-gray-800">开启每日提醒</span>
                            <p class="text-sm text-gray-500">每天20:00提醒未打卡成员</p>
                        </div>
                        <div class="w-12 h-6 bg-blue-500 rounded-full relative">
                            <div class="w-5 h-5 bg-white rounded-full absolute right-0.5 top-0.5"></div>
                        </div>
                    </div>
                </div>

                <!-- 隐私设置 -->
                <div class="bg-white rounded-xl p-5 mb-8 card-shadow">
                    <h3 class="font-semibold text-gray-800 mb-4">隐私设置</h3>
                    
                    <div class="space-y-4">
                        <div class="flex items-center justify-between">
                            <div>
                                <span class="font-medium text-gray-800">公开小组</span>
                                <p class="text-sm text-gray-500">任何人都可以搜索并申请加入</p>
                            </div>
                            <input type="radio" name="privacy" class="text-blue-500" checked>
                        </div>
                        
                        <div class="flex items-center justify-between">
                            <div>
                                <span class="font-medium text-gray-800">私密小组</span>
                                <p class="text-sm text-gray-500">仅通过邀请码加入</p>
                            </div>
                            <input type="radio" name="privacy" class="text-blue-500">
                        </div>
                    </div>
                </div>

                <!-- 创建按钮 -->
                <button class="w-full gradient-bg text-white py-4 rounded-xl font-semibold text-lg shadow-lg">
                    创建小组
                </button>
            </div>

            <!-- 底部导航栏 -->
            <div class="absolute bottom-0 left-0 right-0 bg-white border-t border-gray-100 px-6 py-3">
                <div class="flex justify-around items-center">
                    <div class="flex flex-col items-center">
                        <i class="fas fa-home text-gray-400 text-lg mb-1"></i>
                        <span class="text-xs text-gray-400">首页</span>
                    </div>
                    <div class="flex flex-col items-center">
                        <i class="fas fa-users text-blue-500 text-lg mb-1"></i>
                        <span class="text-xs text-blue-500 font-medium">小组</span>
                    </div>
                    <div class="flex flex-col items-center">
                        <i class="fas fa-chart-bar text-gray-400 text-lg mb-1"></i>
                        <span class="text-xs text-gray-400">统计</span>
                    </div>
                    <div class="flex flex-col items-center">
                        <i class="fas fa-user text-gray-400 text-lg mb-1"></i>
                        <span class="text-xs text-gray-400">我的</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
