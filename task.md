# 微信小程序打卡系统开发任务清单

## 待办事项

### 待办事项1：产品功能设计 ✅
- 初始信息：我是你的产品设计助手，现在请你告诉我，你想开发什么样的产品吧~
- 分析用户发送的信息，对不明确的细节进行追问
- 结合追问得到的答案，加以详细描述形成【产品设计文档.md】文件

### 待办事项2：交互设计 ✅
结合{待办事项1}输出的最终功能，确定该产品包含的所有页面，以下方示例格式输出全部页面的信息。
示例格式：
<页面名称>
用途：<页面的主要作用>
核心功能：<列出该页面包含的主要功能>

所有页面的信息输出完成后，更新【产品设计文档.md】

### 待办事项3：UI设计 ✅
- 根据【产品设计文档.md】，同时遵守下方{UI设计风格}和{UI设计规格}，为每个设计图创建独立的html文件。

全部页面的html文件输出完成后，中断任务，提示用户输出"继续"指令

### 待办事项4：提示用户输入"继续"指令 ✅

### 待办事项5：创建一个UI.html文件 ✅
- UI.html页面的整体背景色为#f6f6f6
- 使用iframe技术将所有页面以适当的网格排列在UI.html里面，每个iframe的宽度固定为400px，高度固定为850px，保证每张设计图完整显示出来，不会被裁切或遮挡。
- iframe的背景色为##f6f6f6

### 待办事项6：自检代码 ✅
依次检查每个页面的html文件，
检查：强制隐藏所有滚动条
检查：设计图尺寸为 375x812PX
检查：信号栏与标题栏背景色必须一致（可以都是透明）
检查：图标和其他样式调用方式
检查：底部tab栏必须为白色填充，100%不透明度。

### 待办事项7：检查UI.html文件 ✅
检查UI.html文件的全部代码，删除iframe之外的多余装饰性元素，每张设计图的内部已经带有了mock up的样式代码，UI.html内的iframe不需要带有mock up的样式，如果有也需要进行删除。

### 待办事项8：微信小程序前端开发 ✅
- 基于产品设计文档，开发微信小程序前端
- 实现用户系统、小组管理、打卡功能、统计与提醒等核心功能
- 使用mock数据进行前端展示
- 确保小程序符合微信小程序开发规范

## 项目信息

**项目类型**: 微信小程序打卡系统
**技术栈**: 微信小程序原生开发 + TypeScript
**主要功能**: 
- 用户系统（微信授权登录）
- 小组管理（创建/加入小组）
- 打卡功能（每日打卡、图片上传）
- 统计与提醒（月度统计、自动提醒）

**用户角色**:
- 管理员：创建/管理小组、设置打卡规则、查看所有成员数据
- 普通成员：加入小组、每日打卡、查看个人数据
