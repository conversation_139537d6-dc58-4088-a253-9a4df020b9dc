<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>个人中心 - 打卡小程序</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body { overflow: hidden; }
        ::-webkit-scrollbar { display: none; }
        .gradient-bg { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .card-shadow { box-shadow: 0 4px 20px rgba(0,0,0,0.08); }
    </style>
</head>
<body class="bg-gray-50">
    <!-- 手机模拟器外框 -->
    <div class="mx-auto bg-black rounded-3xl p-2 shadow-2xl" style="width: 375px; height: 812px;">
        <div class="w-full h-full bg-white rounded-2xl overflow-hidden relative">
            <!-- 状态栏 -->
            <div class="flex justify-between items-center px-6 pt-3 pb-1 bg-transparent text-black text-sm font-medium">
                <span>9:41</span>
                <div class="flex items-center space-x-1">
                    <i class="fas fa-signal text-xs"></i>
                    <i class="fas fa-wifi text-xs"></i>
                    <i class="fas fa-battery-three-quarters text-xs"></i>
                </div>
            </div>

            <!-- 导航栏 -->
            <div class="flex items-center justify-between px-6 py-4 bg-transparent">
                <h1 class="text-xl font-bold text-gray-800">个人中心</h1>
                <button class="w-8 h-8 flex items-center justify-center">
                    <i class="fas fa-cog text-gray-600"></i>
                </button>
            </div>

            <!-- 主要内容区域 -->
            <div class="px-6 pb-20 h-full overflow-y-auto">
                <!-- 用户信息卡片 -->
                <div class="gradient-bg rounded-2xl p-6 mb-6 text-white card-shadow">
                    <div class="flex items-center mb-4">
                        <img src="https://images.unsplash.com/photo-1494790108755-2616b612b786?w=80&h=80&fit=crop&crop=face" class="w-20 h-20 rounded-full border-4 border-white/20 mr-4">
                        <div>
                            <h2 class="text-xl font-bold mb-1">张小美</h2>
                            <p class="text-white/80 text-sm">ID: 123456</p>
                            <p class="text-white/80 text-sm">加入时间: 2024.01.15</p>
                        </div>
                    </div>
                    <div class="grid grid-cols-3 gap-4">
                        <div class="text-center">
                            <div class="text-2xl font-bold mb-1">128</div>
                            <div class="text-white/80 text-xs">总打卡</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold mb-1">3</div>
                            <div class="text-white/80 text-xs">参与小组</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold mb-1">15</div>
                            <div class="text-white/80 text-xs">最长连续</div>
                        </div>
                    </div>
                </div>

                <!-- 功能菜单 -->
                <div class="bg-white rounded-2xl p-5 mb-6 card-shadow">
                    <div class="space-y-4">
                        <div class="flex items-center justify-between py-2">
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                                    <i class="fas fa-calendar-check text-blue-500"></i>
                                </div>
                                <span class="font-medium text-gray-800">我的打卡记录</span>
                            </div>
                            <i class="fas fa-chevron-right text-gray-400"></i>
                        </div>

                        <div class="flex items-center justify-between py-2">
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                                    <i class="fas fa-trophy text-green-500"></i>
                                </div>
                                <span class="font-medium text-gray-800">我的成就</span>
                            </div>
                            <i class="fas fa-chevron-right text-gray-400"></i>
                        </div>

                        <div class="flex items-center justify-between py-2">
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center mr-3">
                                    <i class="fas fa-users text-purple-500"></i>
                                </div>
                                <span class="font-medium text-gray-800">我的小组</span>
                            </div>
                            <i class="fas fa-chevron-right text-gray-400"></i>
                        </div>

                        <div class="flex items-center justify-between py-2">
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-yellow-100 rounded-lg flex items-center justify-center mr-3">
                                    <i class="fas fa-bell text-yellow-500"></i>
                                </div>
                                <span class="font-medium text-gray-800">消息通知</span>
                            </div>
                            <div class="flex items-center">
                                <span class="bg-red-500 text-white text-xs px-2 py-1 rounded-full mr-2">3</span>
                                <i class="fas fa-chevron-right text-gray-400"></i>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 设置选项 -->
                <div class="bg-white rounded-2xl p-5 mb-6 card-shadow">
                    <h3 class="font-semibold text-gray-800 mb-4">设置</h3>
                    <div class="space-y-4">
                        <div class="flex items-center justify-between py-2">
                            <div class="flex items-center">
                                <i class="fas fa-bell text-gray-500 mr-3"></i>
                                <span class="font-medium text-gray-800">推送通知</span>
                            </div>
                            <div class="w-12 h-6 bg-blue-500 rounded-full relative">
                                <div class="w-5 h-5 bg-white rounded-full absolute right-0.5 top-0.5"></div>
                            </div>
                        </div>

                        <div class="flex items-center justify-between py-2">
                            <div class="flex items-center">
                                <i class="fas fa-moon text-gray-500 mr-3"></i>
                                <span class="font-medium text-gray-800">夜间模式</span>
                            </div>
                            <div class="w-12 h-6 bg-gray-300 rounded-full relative">
                                <div class="w-5 h-5 bg-white rounded-full absolute left-0.5 top-0.5"></div>
                            </div>
                        </div>

                        <div class="flex items-center justify-between py-2">
                            <div class="flex items-center">
                                <i class="fas fa-shield-alt text-gray-500 mr-3"></i>
                                <span class="font-medium text-gray-800">隐私设置</span>
                            </div>
                            <i class="fas fa-chevron-right text-gray-400"></i>
                        </div>

                        <div class="flex items-center justify-between py-2">
                            <div class="flex items-center">
                                <i class="fas fa-question-circle text-gray-500 mr-3"></i>
                                <span class="font-medium text-gray-800">帮助与反馈</span>
                            </div>
                            <i class="fas fa-chevron-right text-gray-400"></i>
                        </div>

                        <div class="flex items-center justify-between py-2">
                            <div class="flex items-center">
                                <i class="fas fa-info-circle text-gray-500 mr-3"></i>
                                <span class="font-medium text-gray-800">关于我们</span>
                            </div>
                            <i class="fas fa-chevron-right text-gray-400"></i>
                        </div>
                    </div>
                </div>

                <!-- 退出登录 -->
                <button class="w-full bg-red-50 text-red-500 py-4 rounded-xl font-semibold border border-red-200">
                    退出登录
                </button>
            </div>

            <!-- 底部导航栏 -->
            <div class="absolute bottom-0 left-0 right-0 bg-white border-t border-gray-100 px-6 py-3">
                <div class="flex justify-around items-center">
                    <div class="flex flex-col items-center">
                        <i class="fas fa-home text-gray-400 text-lg mb-1"></i>
                        <span class="text-xs text-gray-400">首页</span>
                    </div>
                    <div class="flex flex-col items-center">
                        <i class="fas fa-users text-gray-400 text-lg mb-1"></i>
                        <span class="text-xs text-gray-400">小组</span>
                    </div>
                    <div class="flex flex-col items-center">
                        <i class="fas fa-chart-bar text-gray-400 text-lg mb-1"></i>
                        <span class="text-xs text-gray-400">统计</span>
                    </div>
                    <div class="flex flex-col items-center">
                        <i class="fas fa-user text-blue-500 text-lg mb-1"></i>
                        <span class="text-xs text-blue-500 font-medium">我的</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
