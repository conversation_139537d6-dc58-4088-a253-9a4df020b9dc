<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>小组详情 - 打卡小程序</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body { overflow: hidden; }
        ::-webkit-scrollbar { display: none; }
        .gradient-bg { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .card-shadow { box-shadow: 0 4px 20px rgba(0,0,0,0.08); }
        .progress-bar { background: linear-gradient(90deg, #10b981 0%, #34d399 100%); }
    </style>
</head>
<body class="bg-gray-50">
    <!-- 手机模拟器外框 -->
    <div class="w-96 h-screen mx-auto bg-black rounded-3xl p-2 shadow-2xl">
        <div class="w-full h-full bg-white rounded-2xl overflow-hidden relative">
            <!-- 状态栏 -->
            <div class="flex justify-between items-center px-6 pt-3 pb-1 bg-transparent text-black text-sm font-medium">
                <span>9:41</span>
                <div class="flex items-center space-x-1">
                    <i class="fas fa-signal text-xs"></i>
                    <i class="fas fa-wifi text-xs"></i>
                    <i class="fas fa-battery-three-quarters text-xs"></i>
                </div>
            </div>

            <!-- 导航栏 -->
            <div class="flex items-center justify-between px-6 py-4 bg-transparent">
                <button class="w-8 h-8 flex items-center justify-center">
                    <i class="fas fa-arrow-left text-gray-600"></i>
                </button>
                <h1 class="text-lg font-semibold text-gray-800">小组详情</h1>
                <button class="w-8 h-8 flex items-center justify-center">
                    <i class="fas fa-ellipsis-h text-gray-600"></i>
                </button>
            </div>

            <!-- 主要内容区域 -->
            <div class="px-6 pb-20 h-full overflow-y-auto">
                <!-- 小组头部信息 -->
                <div class="gradient-bg rounded-2xl p-6 mb-6 text-white card-shadow">
                    <div class="flex items-center mb-4">
                        <div class="w-16 h-16 bg-white/20 rounded-2xl flex items-center justify-center mr-4">
                            <i class="fas fa-running text-2xl"></i>
                        </div>
                        <div>
                            <h2 class="text-xl font-bold mb-1">晨跑俱乐部</h2>
                            <p class="text-white/80">32名成员 · 管理员</p>
                        </div>
                    </div>
                    <p class="text-white/90 text-sm mb-4">每天早上6点一起晨跑，养成健康生活习惯，欢迎大家积极参与！</p>
                    <div class="flex space-x-4">
                        <button class="flex-1 bg-white/20 backdrop-blur-sm rounded-xl py-2 text-sm font-medium">
                            <i class="fas fa-qrcode mr-2"></i>邀请成员
                        </button>
                        <button class="flex-1 bg-white/20 backdrop-blur-sm rounded-xl py-2 text-sm font-medium">
                            <i class="fas fa-cog mr-2"></i>管理设置
                        </button>
                    </div>
                </div>

                <!-- 本月统计 -->
                <div class="bg-white rounded-2xl p-5 mb-6 card-shadow">
                    <h3 class="font-semibold text-gray-800 mb-4">本月统计</h3>
                    <div class="grid grid-cols-2 gap-4 mb-4">
                        <div class="text-center">
                            <div class="text-2xl font-bold text-blue-500 mb-1">22</div>
                            <div class="text-sm text-gray-500">目标天数</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-green-500 mb-1">18</div>
                            <div class="text-sm text-gray-500">已完成</div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="flex justify-between text-sm mb-2">
                            <span class="text-gray-600">整体完成率</span>
                            <span class="font-medium text-gray-800">82%</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div class="progress-bar h-2 rounded-full" style="width: 82%"></div>
                        </div>
                    </div>
                </div>

                <!-- 成员列表 -->
                <div class="bg-white rounded-2xl p-5 card-shadow">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="font-semibold text-gray-800">成员列表</h3>
                        <span class="text-sm text-gray-500">32人</span>
                    </div>
                    
                    <div class="space-y-3">
                        <!-- 成员项 -->
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <img src="https://images.unsplash.com/photo-1494790108755-2616b612b786?w=40&h=40&fit=crop&crop=face" class="w-10 h-10 rounded-full mr-3">
                                <div>
                                    <div class="flex items-center">
                                        <span class="font-medium text-gray-800 mr-2">张小美</span>
                                        <span class="bg-yellow-100 text-yellow-600 text-xs px-2 py-1 rounded-full">管理员</span>
                                    </div>
                                    <p class="text-sm text-gray-500">本月 22/22 次</p>
                                </div>
                            </div>
                            <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                                <i class="fas fa-check text-green-500 text-sm"></i>
                            </div>
                        </div>

                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=40&h=40&fit=crop&crop=face" class="w-10 h-10 rounded-full mr-3">
                                <div>
                                    <span class="font-medium text-gray-800 block">李小明</span>
                                    <p class="text-sm text-gray-500">本月 20/22 次</p>
                                </div>
                            </div>
                            <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                                <i class="fas fa-check text-green-500 text-sm"></i>
                            </div>
                        </div>

                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <img src="https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=40&h=40&fit=crop&crop=face" class="w-10 h-10 rounded-full mr-3">
                                <div>
                                    <span class="font-medium text-gray-800 block">王小红</span>
                                    <p class="text-sm text-gray-500">本月 18/22 次</p>
                                </div>
                            </div>
                            <div class="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                                <i class="fas fa-clock text-yellow-500 text-sm"></i>
                            </div>
                        </div>

                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop&crop=face" class="w-10 h-10 rounded-full mr-3">
                                <div>
                                    <span class="font-medium text-gray-800 block">刘小强</span>
                                    <p class="text-sm text-red-500">本月 12/22 次</p>
                                </div>
                            </div>
                            <div class="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                                <i class="fas fa-exclamation text-red-500 text-sm"></i>
                            </div>
                        </div>

                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <img src="https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=40&h=40&fit=crop&crop=face" class="w-10 h-10 rounded-full mr-3">
                                <div>
                                    <span class="font-medium text-gray-800 block">陈小花</span>
                                    <p class="text-sm text-gray-500">本月 19/22 次</p>
                                </div>
                            </div>
                            <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                                <i class="fas fa-check text-green-500 text-sm"></i>
                            </div>
                        </div>
                    </div>

                    <button class="w-full mt-4 text-blue-500 text-sm font-medium py-2">
                        查看全部成员
                    </button>
                </div>
            </div>

            <!-- 底部导航栏 -->
            <div class="absolute bottom-0 left-0 right-0 bg-white border-t border-gray-100 px-6 py-3">
                <div class="flex justify-around items-center">
                    <div class="flex flex-col items-center">
                        <i class="fas fa-home text-gray-400 text-lg mb-1"></i>
                        <span class="text-xs text-gray-400">首页</span>
                    </div>
                    <div class="flex flex-col items-center">
                        <i class="fas fa-users text-blue-500 text-lg mb-1"></i>
                        <span class="text-xs text-blue-500 font-medium">小组</span>
                    </div>
                    <div class="flex flex-col items-center">
                        <i class="fas fa-chart-bar text-gray-400 text-lg mb-1"></i>
                        <span class="text-xs text-gray-400">统计</span>
                    </div>
                    <div class="flex flex-col items-center">
                        <i class="fas fa-user text-gray-400 text-lg mb-1"></i>
                        <span class="text-xs text-gray-400">我的</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
