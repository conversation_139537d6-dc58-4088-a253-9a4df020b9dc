# 微信小程序打卡系统接口文档

## 1. 用户相关接口

### 1.1 微信登录
**请求方法**: POST  
**接口URL**: `/api/auth/login`  
**请求参数**:
```json
{
  "code": "微信授权码",
  "encryptedData": "加密数据",
  "iv": "初始向量"
}
```
**返回参数**:
```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "token": "用户token",
    "userInfo": {
      "userId": "用户ID",
      "nickName": "用户昵称",
      "avatarUrl": "头像URL",
      "openId": "微信openId"
    }
  }
}
```

### 1.2 获取用户信息
**请求方法**: GET  
**接口URL**: `/api/user/profile`  
**请求参数**: 无  
**返回参数**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "userId": "用户ID",
    "nickName": "用户昵称",
    "avatarUrl": "头像URL",
    "joinDate": "加入时间",
    "totalCheckins": 128,
    "joinedGroups": 3,
    "maxConsecutive": 15
  }
}
```

### 1.3 更新用户信息
**请求方法**: PUT  
**接口URL**: `/api/user/profile`  
**请求参数**:
```json
{
  "nickName": "新昵称",
  "avatarUrl": "新头像URL"
}
```
**返回参数**:
```json
{
  "code": 200,
  "message": "更新成功",
  "data": null
}
```

## 2. 小组相关接口

### 2.1 创建小组
**请求方法**: POST  
**接口URL**: `/api/groups`  
**请求参数**:
```json
{
  "name": "小组名称",
  "description": "小组描述",
  "icon": "小组图标",
  "iconColor": "图标颜色",
  "targetCount": 22,
  "enableReminder": true,
  "isPublic": true
}
```
**返回参数**:
```json
{
  "code": 200,
  "message": "创建成功",
  "data": {
    "groupId": "小组ID",
    "inviteCode": "邀请码",
    "qrCodeUrl": "二维码URL"
  }
}
```

### 2.2 获取小组列表
**请求方法**: GET  
**接口URL**: `/api/groups`  
**请求参数**: 无  
**返回参数**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": [
    {
      "id": "小组ID",
      "name": "小组名称",
      "description": "小组描述",
      "icon": "小组图标",
      "iconColor": "图标颜色",
      "memberCount": 32,
      "role": "管理员",
      "currentCount": 18,
      "targetCount": 22,
      "completionRate": 82
    }
  ]
}
```

### 2.3 获取小组详情
**请求方法**: GET  
**接口URL**: `/api/groups/{groupId}`  
**请求参数**: 无  
**返回参数**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "id": "小组ID",
    "name": "小组名称",
    "description": "小组描述",
    "icon": "小组图标",
    "iconColor": "图标颜色",
    "memberCount": 32,
    "role": "用户角色",
    "targetCount": 22,
    "enableReminder": true,
    "isPublic": true,
    "createdAt": "创建时间",
    "members": [
      {
        "userId": "用户ID",
        "nickName": "用户昵称",
        "avatarUrl": "头像URL",
        "role": "角色",
        "currentCount": 18,
        "targetCount": 22,
        "joinedAt": "加入时间"
      }
    ]
  }
}
```

### 2.4 加入小组
**请求方法**: POST  
**接口URL**: `/api/groups/{groupId}/join`  
**请求参数**:
```json
{
  "inviteCode": "邀请码"
}
```
**返回参数**:
```json
{
  "code": 200,
  "message": "加入成功",
  "data": null
}
```

### 2.5 退出小组
**请求方法**: DELETE  
**接口URL**: `/api/groups/{groupId}/leave`  
**请求参数**: 无  
**返回参数**:
```json
{
  "code": 200,
  "message": "退出成功",
  "data": null
}
```

## 3. 打卡相关接口

### 3.1 提交打卡
**请求方法**: POST  
**接口URL**: `/api/checkins`  
**请求参数**:
```json
{
  "groupId": "小组ID",
  "imageUrl": "图片URL",
  "note": "备注内容",
  "location": "位置信息"
}
```
**返回参数**:
```json
{
  "code": 200,
  "message": "打卡成功",
  "data": {
    "checkinId": "打卡记录ID",
    "date": "打卡日期",
    "timestamp": "打卡时间戳"
  }
}
```

### 3.2 获取打卡记录
**请求方法**: GET  
**接口URL**: `/api/checkins`  
**请求参数**:
```json
{
  "year": 2024,
  "month": 6,
  "groupId": "小组ID(可选)"
}
```
**返回参数**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": [
    {
      "id": "打卡记录ID",
      "date": "打卡日期",
      "imageUrl": "图片URL",
      "note": "备注内容",
      "location": "位置信息",
      "groupId": "小组ID",
      "groupName": "小组名称",
      "timestamp": "打卡时间戳"
    }
  ]
}
```

### 3.3 获取打卡统计
**请求方法**: GET  
**接口URL**: `/api/checkins/statistics`  
**请求参数**:
```json
{
  "year": 2024,
  "month": 6,
  "groupId": "小组ID(可选)"
}
```
**返回参数**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "totalCheckins": 18,
    "consecutiveDays": 7,
    "completionRate": 82,
    "weeklyTrend": [
      {"day": "周一", "count": 3},
      {"day": "周二", "count": 4},
      {"day": "周三", "count": 5},
      {"day": "周四", "count": 3},
      {"day": "周五", "count": 4},
      {"day": "周六", "count": 2},
      {"day": "周日", "count": 1}
    ]
  }
}
```

## 4. 通知相关接口

### 4.1 获取通知列表
**请求方法**: GET  
**接口URL**: `/api/notifications`  
**请求参数**:
```json
{
  "type": "通知类型(可选)",
  "page": 1,
  "limit": 20
}
```
**返回参数**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "list": [
      {
        "id": "通知ID",
        "type": "通知类型",
        "title": "通知标题",
        "message": "通知内容",
        "read": false,
        "timestamp": "时间戳"
      }
    ],
    "total": 50,
    "hasMore": true
  }
}
```

### 4.2 标记通知已读
**请求方法**: PUT  
**接口URL**: `/api/notifications/{notificationId}/read`  
**请求参数**: 无  
**返回参数**:
```json
{
  "code": 200,
  "message": "标记成功",
  "data": null
}
```

### 4.3 批量标记已读
**请求方法**: PUT  
**接口URL**: `/api/notifications/read-all`  
**请求参数**: 无  
**返回参数**:
```json
{
  "code": 200,
  "message": "标记成功",
  "data": null
}
```

## 5. 文件上传接口

### 5.1 上传图片
**请求方法**: POST  
**接口URL**: `/api/upload/image`  
**请求参数**: FormData (multipart/form-data)
```
file: 图片文件
```
**返回参数**:
```json
{
  "code": 200,
  "message": "上传成功",
  "data": {
    "url": "图片URL",
    "size": 1024000,
    "format": "jpg"
  }
}
```

## 6. 错误码说明

| 错误码 | 说明 |
|--------|------|
| 200 | 成功 |
| 400 | 请求参数错误 |
| 401 | 未授权 |
| 403 | 禁止访问 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |
| 1001 | 用户不存在 |
| 1002 | 小组不存在 |
| 1003 | 今日已打卡 |
| 1004 | 邀请码无效 |
| 1005 | 文件格式不支持 |
| 1006 | 文件大小超限 |
