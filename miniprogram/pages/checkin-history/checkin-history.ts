// checkin-history.ts

Component({
  data: {
    currentYear: 0,
    currentMonth: 0,
    currentMonth: '',
    canGoNext: false,
    weekDays: ['日', '一', '二', '三', '四', '五', '六'],
    calendarDays: [] as any[],
    monthStats: {
      checkedDays: 0,
      missedDays: 0,
      completionRate: 0
    },
    selectedDateDetail: null as any,
    checkinHistory: [] as any[]
  },

  lifetimes: {
    attached() {
      this.initCalendar()
      this.loadCheckinHistory()
    }
  },

  methods: {
    // 初始化日历
    initCalendar() {
      const now = new Date()
      this.setData({
        currentYear: now.getFullYear(),
        currentMonth: now.getMonth()
      })
      this.generateCalendar()
    },

    // 生成日历
    generateCalendar() {
      const { currentYear, currentMonth } = this.data
      const now = new Date()
      const firstDay = new Date(currentYear, currentMonth, 1)
      const lastDay = new Date(currentYear, currentMonth + 1, 0)
      const startDate = new Date(firstDay)
      startDate.setDate(startDate.getDate() - firstDay.getDay())

      const calendarDays = []
      const checkinData = this.getCheckinData()

      // 生成6周的日期
      for (let week = 0; week < 6; week++) {
        for (let day = 0; day < 7; day++) {
          const currentDate = new Date(startDate)
          currentDate.setDate(startDate.getDate() + week * 7 + day)
          
          const dateKey = this.formatDateKey(currentDate)
          const hasCheckin = checkinData[dateKey] || false
          const isCurrentMonth = currentDate.getMonth() === currentMonth
          const isToday = this.isSameDate(currentDate, now)

          let type = 'current-month'
          if (!isCurrentMonth) {
            type = 'other-month'
          } else if (isToday) {
            type = 'today'
          } else if (hasCheckin) {
            type = 'checked'
          }

          calendarDays.push({
            date: dateKey,
            day: currentDate.getDate(),
            type,
            hasCheckin,
            status: hasCheckin ? 'completed' : '',
            statusIcon: hasCheckin ? '✓' : ''
          })
        }
      }

      // 计算月度统计
      const monthStats = this.calculateMonthStats(checkinData, currentYear, currentMonth)

      // 检查是否可以前进到下个月
      const canGoNext = currentMonth < now.getMonth() || currentYear < now.getFullYear()

      this.setData({
        calendarDays,
        monthStats,
        canGoNext,
        currentMonth: `${currentYear}年${currentMonth + 1}月`
      })
    },

    // 获取打卡数据
    getCheckinData() {
      const history = wx.getStorageSync('checkin_history') || []
      const checkinData: Record<string, boolean> = {}
      
      history.forEach((record: any) => {
        if (record.date) {
          checkinData[record.date] = true
        }
      })
      
      return checkinData
    },

    // 计算月度统计
    calculateMonthStats(checkinData: Record<string, boolean>, year: number, month: number) {
      const daysInMonth = new Date(year, month + 1, 0).getDate()
      const today = new Date()
      const isCurrentMonth = year === today.getFullYear() && month === today.getMonth()
      const currentDay = isCurrentMonth ? today.getDate() : daysInMonth

      let checkedDays = 0
      for (let day = 1; day <= currentDay; day++) {
        const dateKey = `${year}-${month + 1}-${day}`
        if (checkinData[dateKey]) {
          checkedDays++
        }
      }

      const missedDays = currentDay - checkedDays
      const completionRate = currentDay > 0 ? Math.round((checkedDays / currentDay) * 100) : 0

      return {
        checkedDays,
        missedDays,
        completionRate
      }
    },

    // 格式化日期键
    formatDateKey(date: Date): string {
      return `${date.getFullYear()}-${date.getMonth() + 1}-${date.getDate()}`
    },

    // 判断是否是同一天
    isSameDate(date1: Date, date2: Date): boolean {
      return date1.getFullYear() === date2.getFullYear() &&
             date1.getMonth() === date2.getMonth() &&
             date1.getDate() === date2.getDate()
    },

    // 上一个月
    prevMonth() {
      let { currentYear, currentMonth } = this.data
      currentMonth--
      if (currentMonth < 0) {
        currentMonth = 11
        currentYear--
      }
      this.setData({
        currentYear,
        currentMonth,
        selectedDateDetail: null
      })
      this.generateCalendar()
    },

    // 下一个月
    nextMonth() {
      if (!this.data.canGoNext) return
      
      let { currentYear, currentMonth } = this.data
      currentMonth++
      if (currentMonth > 11) {
        currentMonth = 0
        currentYear++
      }
      this.setData({
        currentYear,
        currentMonth,
        selectedDateDetail: null
      })
      this.generateCalendar()
    },

    // 选择日期
    selectDate(e: any) {
      const { date, hasCheckin } = e.currentTarget.dataset
      
      if (hasCheckin) {
        this.showDateDetail(date)
      } else {
        this.setData({
          selectedDateDetail: {
            dateText: this.formatDateText(date),
            hasCheckin: false,
            status: 'missed',
            statusText: '未打卡'
          }
        })
      }
    },

    // 显示日期详情
    showDateDetail(dateKey: string) {
      const history = wx.getStorageSync('checkin_history') || []
      const record = history.find((item: any) => item.date === dateKey)
      
      if (record) {
        this.setData({
          selectedDateDetail: {
            dateText: this.formatDateText(dateKey),
            hasCheckin: true,
            status: 'completed',
            statusText: '已完成',
            imageUrl: record.imageUrl,
            note: record.note,
            time: this.formatTime(record.timestamp),
            location: '中央公园', // Mock数据
            groupName: '晨跑俱乐部' // Mock数据
          }
        })
      }
    },

    // 格式化日期文本
    formatDateText(dateKey: string): string {
      const [year, month, day] = dateKey.split('-')
      return `${year}年${month}月${day}日`
    },

    // 格式化时间
    formatTime(timestamp: number): string {
      const date = new Date(timestamp)
      const hours = date.getHours().toString().padStart(2, '0')
      const minutes = date.getMinutes().toString().padStart(2, '0')
      return `${hours}:${minutes}`
    },

    // 加载打卡历史
    loadCheckinHistory() {
      const history = wx.getStorageSync('checkin_history') || []
      const formattedHistory = history.slice(0, 10).map((record: any) => ({
        ...record,
        dateText: this.formatDateText(record.date),
        status: 'completed',
        statusIcon: '✓',
        groupName: '晨跑俱乐部' // Mock数据
      }))
      
      this.setData({
        checkinHistory: formattedHistory
      })
    },

    // 查看历史详情
    viewHistoryDetail(e: any) {
      const item = e.currentTarget.dataset.item
      this.showDateDetail(item.date)
    },

    // 去打卡
    goToCheckin() {
      wx.navigateTo({
        url: '/pages/checkin/checkin'
      })
    }
  }
})
