<!--checkin-history.wxml-->
<view class="container">
  <!-- 月份导航 -->
  <view class="month-nav">
    <button class="nav-btn" bindtap="prevMonth">
      <text class="nav-icon">‹</text>
    </button>
    <text class="month-title">{{currentMonth}}</text>
    <button class="nav-btn" bindtap="nextMonth" disabled="{{!canGoNext}}">
      <text class="nav-icon">›</text>
    </button>
  </view>

  <!-- 日历视图 -->
  <view class="calendar-card">
    <!-- 星期标题 -->
    <view class="week-header">
      <text class="week-day" wx:for="{{weekDays}}" wx:key="*this">{{item}}</text>
    </view>
    
    <!-- 日期网格 -->
    <view class="calendar-grid">
      <view 
        class="calendar-day {{item.type}}" 
        wx:for="{{calendarDays}}" 
        wx:key="date"
        bindtap="selectDate"
        data-date="{{item.date}}"
        data-has-checkin="{{item.hasCheckin}}"
      >
        <text class="day-number">{{item.day}}</text>
        <view class="day-indicator {{item.status}}" wx:if="{{item.hasCheckin}}">
          <text class="indicator-icon">{{item.statusIcon}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 本月统计 -->
  <view class="stats-card">
    <text class="card-title">本月统计</text>
    <view class="stats-grid">
      <view class="stat-item">
        <text class="stat-number checked">{{monthStats.checkedDays}}</text>
        <text class="stat-label">已打卡</text>
      </view>
      <view class="stat-item">
        <text class="stat-number missed">{{monthStats.missedDays}}</text>
        <text class="stat-label">未打卡</text>
      </view>
      <view class="stat-item">
        <text class="stat-number rate">{{monthStats.completionRate}}%</text>
        <text class="stat-label">完成率</text>
      </view>
    </view>
  </view>

  <!-- 选中日期的打卡详情 -->
  <view class="detail-card" wx:if="{{selectedDateDetail}}">
    <view class="detail-header">
      <text class="detail-title">{{selectedDateDetail.dateText}} 打卡详情</text>
      <view class="detail-status {{selectedDateDetail.status}}">
        <text>{{selectedDateDetail.statusText}}</text>
      </view>
    </view>
    
    <view class="detail-content" wx:if="{{selectedDateDetail.hasCheckin}}">
      <view class="detail-image" wx:if="{{selectedDateDetail.imageUrl}}">
        <image src="{{selectedDateDetail.imageUrl}}" mode="aspectFill" class="checkin-image"></image>
      </view>
      
      <view class="detail-note" wx:if="{{selectedDateDetail.note}}">
        <text class="note-text">{{selectedDateDetail.note}}</text>
      </view>
      
      <view class="detail-meta">
        <view class="meta-item">
          <text class="meta-icon">🕐</text>
          <text class="meta-text">{{selectedDateDetail.time}}</text>
        </view>
        <view class="meta-item" wx:if="{{selectedDateDetail.location}}">
          <text class="meta-icon">📍</text>
          <text class="meta-text">{{selectedDateDetail.location}}</text>
        </view>
        <view class="meta-item">
          <text class="meta-icon">👥</text>
          <text class="meta-text">{{selectedDateDetail.groupName}}</text>
        </view>
      </view>
    </view>

    <view class="no-checkin" wx:else>
      <view class="no-checkin-icon">📅</view>
      <text class="no-checkin-text">这一天没有打卡记录</text>
    </view>
  </view>

  <!-- 打卡历史列表 -->
  <view class="history-card" wx:if="{{!selectedDateDetail && checkinHistory.length > 0}}">
    <text class="card-title">最近打卡</text>
    <view class="history-list">
      <view 
        class="history-item" 
        wx:for="{{checkinHistory}}" 
        wx:key="id"
        bindtap="viewHistoryDetail"
        data-item="{{item}}"
      >
        <image src="{{item.imageUrl}}" mode="aspectFill" class="history-image"></image>
        <view class="history-info">
          <text class="history-date">{{item.dateText}}</text>
          <text class="history-note">{{item.note || '无备注'}}</text>
          <text class="history-group">{{item.groupName}}</text>
        </view>
        <view class="history-status {{item.status}}">
          <text>{{item.statusIcon}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{checkinHistory.length === 0}}">
    <view class="empty-icon">📝</view>
    <text class="empty-title">还没有打卡记录</text>
    <text class="empty-subtitle">开始你的第一次打卡吧</text>
    <button class="empty-action" bindtap="goToCheckin">立即打卡</button>
  </view>
</view>
