/* checkin-history.wxss */
.container {
  padding: 32rpx;
  background-color: #f6f6f6;
  min-height: 100vh;
}

/* 月份导航 */
.month-nav {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32rpx;
}

.nav-btn {
  width: 64rpx;
  height: 64rpx;
  background: white;
  border: none;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.nav-btn:disabled {
  opacity: 0.5;
}

.nav-icon {
  font-size: 32rpx;
  color: #333;
}

.month-title {
  font-size: 40rpx;
  font-weight: bold;
  color: #333;
}

/* 日历卡片 */
.calendar-card {
  background: white;
  border-radius: 32rpx;
  padding: 40rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 8rpx 40rpx rgba(0, 0, 0, 0.08);
}

.week-header {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 8rpx;
  margin-bottom: 24rpx;
}

.week-day {
  text-align: center;
  font-size: 24rpx;
  color: #666;
  padding: 16rpx 0;
}

.calendar-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 8rpx;
}

.calendar-day {
  aspect-ratio: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-radius: 16rpx;
  position: relative;
  min-height: 80rpx;
}

.calendar-day.current-month {
  color: #333;
}

.calendar-day.other-month {
  color: #ccc;
}

.calendar-day.today {
  background: #667eea;
  color: white;
}

.calendar-day.checked {
  background: linear-gradient(135deg, #4caf50, #81c784);
  color: white;
}

.calendar-day.selected {
  background: #e3f2fd;
  border: 2rpx solid #667eea;
}

.day-number {
  font-size: 28rpx;
  font-weight: 500;
}

.day-indicator {
  position: absolute;
  bottom: 4rpx;
  right: 4rpx;
  width: 24rpx;
  height: 24rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16rpx;
}

.day-indicator.completed {
  background: rgba(255, 255, 255, 0.3);
}

/* 统计卡片 */
.stats-card {
  background: white;
  border-radius: 32rpx;
  padding: 40rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 8rpx 40rpx rgba(0, 0, 0, 0.08);
}

.card-title {
  display: block;
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 32rpx;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 32rpx;
}

.stat-item {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.stat-number.checked {
  color: #4caf50;
}

.stat-number.missed {
  color: #f44336;
}

.stat-number.rate {
  color: #667eea;
}

.stat-label {
  display: block;
  font-size: 24rpx;
  color: #666;
}

/* 详情卡片 */
.detail-card {
  background: white;
  border-radius: 32rpx;
  padding: 40rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 8rpx 40rpx rgba(0, 0, 0, 0.08);
}

.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32rpx;
}

.detail-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.detail-status {
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  font-size: 24rpx;
  font-weight: 500;
}

.detail-status.completed {
  background: #e8f5e8;
  color: #4caf50;
}

.detail-image {
  margin-bottom: 24rpx;
}

.checkin-image {
  width: 100%;
  height: 320rpx;
  border-radius: 16rpx;
}

.detail-note {
  margin-bottom: 24rpx;
}

.note-text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
}

.detail-meta {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 16rpx;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.meta-icon {
  font-size: 24rpx;
}

.meta-text {
  font-size: 24rpx;
  color: #666;
}

.no-checkin {
  text-align: center;
  padding: 40rpx;
}

.no-checkin-icon {
  font-size: 80rpx;
  margin-bottom: 16rpx;
}

.no-checkin-text {
  font-size: 28rpx;
  color: #666;
}

/* 历史列表 */
.history-card {
  background: white;
  border-radius: 32rpx;
  padding: 40rpx;
  box-shadow: 0 8rpx 40rpx rgba(0, 0, 0, 0.08);
}

.history-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.history-item {
  display: flex;
  align-items: center;
  gap: 24rpx;
  padding: 16rpx;
  border-radius: 16rpx;
  transition: background-color 0.2s;
}

.history-item:active {
  background: #f5f5f5;
}

.history-image {
  width: 80rpx;
  height: 80rpx;
  border-radius: 16rpx;
}

.history-info {
  flex: 1;
}

.history-date {
  display: block;
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
}

.history-note {
  display: block;
  font-size: 24rpx;
  color: #666;
  margin-bottom: 4rpx;
}

.history-group {
  display: block;
  font-size: 24rpx;
  color: #999;
}

.history-status {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
}

.history-status.completed {
  background: #e8f5e8;
  color: #4caf50;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 120rpx 40rpx;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 32rpx;
}

.empty-title {
  display: block;
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
}

.empty-subtitle {
  display: block;
  font-size: 28rpx;
  color: #666;
  margin-bottom: 48rpx;
}

.empty-action {
  background: #667eea;
  color: white;
  border: none;
  border-radius: 24rpx;
  padding: 24rpx 48rpx;
  font-size: 32rpx;
  font-weight: 500;
}
