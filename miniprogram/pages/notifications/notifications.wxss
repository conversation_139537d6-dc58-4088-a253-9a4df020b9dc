/* notifications.wxss */
.container {
  padding: 32rpx;
  background-color: #f6f6f6;
  min-height: 100vh;
}

/* 分类标签 */
.category-tabs {
  display: flex;
  gap: 16rpx;
  margin-bottom: 32rpx;
  overflow-x: auto;
  padding-bottom: 8rpx;
}

.tab-btn {
  padding: 16rpx 32rpx;
  border-radius: 32rpx;
  font-size: 28rpx;
  font-weight: 500;
  border: none;
  white-space: nowrap;
  flex-shrink: 0;
}

.tab-btn.active {
  background: #667eea;
  color: white;
  box-shadow: 0 4rpx 20rpx rgba(102, 126, 234, 0.3);
}

.tab-btn.inactive {
  background: white;
  color: #666;
  border: 2rpx solid #e0e0e0;
}

/* 操作栏 */
.action-bar {
  display: flex;
  justify-content: flex-end;
  gap: 16rpx;
  margin-bottom: 24rpx;
}

.action-btn {
  background: white;
  color: #667eea;
  border: 2rpx solid #667eea;
  border-radius: 16rpx;
  padding: 12rpx 24rpx;
  font-size: 24rpx;
  font-weight: 500;
}

/* 通知列表 */
.notifications-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.notification-item {
  background: white;
  border-radius: 24rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  transition: transform 0.2s ease;
}

.notification-item:active {
  transform: scale(0.98);
}

.notification-item.unread {
  border-left: 8rpx solid #667eea;
}

.notification-item.read {
  opacity: 0.7;
}

.notification-content {
  width: 100%;
}

.notification-header {
  display: flex;
  align-items: flex-start;
  margin-bottom: 16rpx;
}

.notification-icon {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
  font-size: 28rpx;
  flex-shrink: 0;
}

.notification-icon.checkin {
  background: #e3f2fd;
  color: #1976d2;
}

.notification-icon.group {
  background: #e8f5e8;
  color: #388e3c;
}

.notification-icon.achievement {
  background: #fff3e0;
  color: #f57c00;
}

.notification-icon.reminder {
  background: #fff3e0;
  color: #ff9800;
}

.notification-icon.system {
  background: #f5f5f5;
  color: #666;
}

.notification-icon.social {
  background: #ffebee;
  color: #f44336;
}

.notification-info {
  flex: 1;
  min-width: 0;
}

.notification-title {
  display: block;
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
}

.notification-message {
  display: block;
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
  word-break: break-all;
}

.unread-indicator {
  width: 16rpx;
  height: 16rpx;
  background: #f44336;
  border-radius: 50%;
  flex-shrink: 0;
  margin-left: 16rpx;
  margin-top: 8rpx;
}

.notification-time {
  display: block;
  font-size: 24rpx;
  color: #999;
  text-align: right;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 120rpx 40rpx;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 32rpx;
  opacity: 0.5;
}

.empty-title {
  display: block;
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
}

.empty-subtitle {
  display: block;
  font-size: 28rpx;
  color: #666;
}

/* 加载更多 */
.load-more {
  text-align: center;
  padding: 40rpx;
}

.load-more-btn {
  background: white;
  color: #667eea;
  border: 2rpx solid #667eea;
  border-radius: 24rpx;
  padding: 24rpx 48rpx;
  font-size: 28rpx;
  font-weight: 500;
}
