<!--create-group.wxml-->
<view class="container">
  <!-- 小组图标选择 -->
  <view class="icon-section">
    <text class="section-label">选择小组图标</text>
    <view class="icon-grid">
      <view 
        class="icon-item {{selectedIcon === item.icon ? 'selected' : ''}}" 
        wx:for="{{iconOptions}}" 
        wx:key="icon"
        bindtap="selectIcon"
        data-icon="{{item.icon}}"
        data-color="{{item.color}}"
      >
        <view class="icon-display {{item.color}}">
          <text>{{item.icon}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 小组名称 -->
  <view class="input-section">
    <text class="section-label">小组名称 *</text>
    <input 
      class="text-input"
      placeholder="请输入小组名称"
      value="{{groupName}}"
      bindinput="onGroupNameInput"
      maxlength="20"
    />
    <text class="input-count">{{groupName.length}}/20</text>
  </view>

  <!-- 小组描述 -->
  <view class="input-section">
    <text class="section-label">小组描述</text>
    <textarea 
      class="textarea-input"
      placeholder="介绍一下这个小组的目标和规则..."
      value="{{groupDescription}}"
      bindinput="onGroupDescriptionInput"
      maxlength="200"
    ></textarea>
    <text class="input-count">{{groupDescription.length}}/200</text>
  </view>

  <!-- 打卡设置 -->
  <view class="settings-card">
    <text class="card-title">打卡设置</text>
    
    <view class="setting-item">
      <text class="setting-label">每月最低打卡次数</text>
      <view class="counter-control">
        <button class="counter-btn" bindtap="decreaseTarget" disabled="{{targetCount <= 1}}">
          -
        </button>
        <text class="counter-value">{{targetCount}}</text>
        <button class="counter-btn" bindtap="increaseTarget" disabled="{{targetCount >= 31}}">
          +
        </button>
      </view>
    </view>

    <view class="setting-item">
      <view class="setting-info">
        <text class="setting-label">开启每日提醒</text>
        <text class="setting-desc">每天20:00提醒未打卡成员</text>
      </view>
      <switch 
        checked="{{enableReminder}}" 
        bindchange="onReminderChange"
        color="#667eea"
      />
    </view>
  </view>

  <!-- 隐私设置 -->
  <view class="privacy-card">
    <text class="card-title">隐私设置</text>
    
    <view class="privacy-options">
      <view 
        class="privacy-option {{isPublic ? 'selected' : ''}}" 
        bindtap="setPrivacy"
        data-public="{{true}}"
      >
        <radio checked="{{isPublic}}" color="#667eea"/>
        <view class="privacy-info">
          <text class="privacy-title">公开小组</text>
          <text class="privacy-desc">任何人都可以搜索并申请加入</text>
        </view>
      </view>
      
      <view 
        class="privacy-option {{!isPublic ? 'selected' : ''}}" 
        bindtap="setPrivacy"
        data-public="{{false}}"
      >
        <radio checked="{{!isPublic}}" color="#667eea"/>
        <view class="privacy-info">
          <text class="privacy-title">私密小组</text>
          <text class="privacy-desc">仅通过邀请码加入</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 创建按钮 -->
  <button 
    class="create-btn {{canCreate ? 'active' : 'disabled'}}" 
    bindtap="createGroup"
    disabled="{{!canCreate || creating}}"
  >
    {{creating ? '创建中...' : '创建小组'}}
  </button>
</view>
