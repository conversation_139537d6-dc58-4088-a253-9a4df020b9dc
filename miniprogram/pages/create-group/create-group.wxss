/* create-group.wxss */
.container {
  padding: 32rpx;
  background-color: #f6f6f6;
  min-height: 100vh;
}

.section-label {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 24rpx;
}

/* 图标选择 */
.icon-section {
  margin-bottom: 48rpx;
}

.icon-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 24rpx;
}

.icon-item {
  display: flex;
  justify-content: center;
  border: 4rpx solid transparent;
  border-radius: 16rpx;
  padding: 8rpx;
}

.icon-item.selected {
  border-color: #667eea;
  background: #f8faff;
}

.icon-display {
  width: 96rpx;
  height: 96rpx;
  border-radius: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
}

.icon-display.blue {
  background: #e3f2fd;
  color: #1976d2;
}

.icon-display.purple {
  background: #f3e5f5;
  color: #7b1fa2;
}

.icon-display.green {
  background: #e8f5e8;
  color: #388e3c;
}

.icon-display.yellow {
  background: #fff3e0;
  color: #f57c00;
}

.icon-display.red {
  background: #ffebee;
  color: #f44336;
}

.icon-display.indigo {
  background: #e8eaf6;
  color: #3f51b5;
}

.icon-display.pink {
  background: #fce4ec;
  color: #e91e63;
}

.icon-display.teal {
  background: #e0f2f1;
  color: #00695c;
}

.icon-display.orange {
  background: #fff3e0;
  color: #ef6c00;
}

.icon-display.gray {
  background: #f5f5f5;
  color: #757575;
}

/* 输入区域 */
.input-section {
  margin-bottom: 48rpx;
}

.text-input {
  width: 100%;
  background: white;
  border-radius: 24rpx;
  padding: 32rpx;
  font-size: 32rpx;
  border: none;
  box-shadow: 0 8rpx 40rpx rgba(0, 0, 0, 0.08);
  box-sizing: border-box;
}

.textarea-input {
  width: 100%;
  min-height: 192rpx;
  background: white;
  border-radius: 24rpx;
  padding: 32rpx;
  font-size: 28rpx;
  border: none;
  box-shadow: 0 8rpx 40rpx rgba(0, 0, 0, 0.08);
  box-sizing: border-box;
}

.input-count {
  display: block;
  text-align: right;
  font-size: 24rpx;
  color: #999;
  margin-top: 16rpx;
}

/* 设置卡片 */
.settings-card,
.privacy-card {
  background: white;
  border-radius: 32rpx;
  padding: 40rpx;
  margin-bottom: 48rpx;
  box-shadow: 0 8rpx 40rpx rgba(0, 0, 0, 0.08);
}

.card-title {
  display: block;
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 32rpx;
}

.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.setting-item:last-child {
  border-bottom: none;
}

.setting-label {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

.setting-info {
  flex: 1;
}

.setting-desc {
  display: block;
  font-size: 24rpx;
  color: #666;
  margin-top: 4rpx;
}

/* 计数器控制 */
.counter-control {
  display: flex;
  align-items: center;
  gap: 24rpx;
}

.counter-btn {
  width: 80rpx;
  height: 80rpx;
  background: #f5f5f5;
  border: none;
  border-radius: 16rpx;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  display: flex;
  align-items: center;
  justify-content: center;
}

.counter-btn:disabled {
  opacity: 0.5;
}

.counter-value {
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
  min-width: 80rpx;
  text-align: center;
}

/* 隐私设置 */
.privacy-options {
  display: flex;
  flex-direction: column;
  gap: 32rpx;
}

.privacy-option {
  display: flex;
  align-items: flex-start;
  padding: 24rpx;
  border: 4rpx solid transparent;
  border-radius: 24rpx;
}

.privacy-option.selected {
  border-color: #667eea;
  background: #f8faff;
}

.privacy-option radio {
  margin-right: 24rpx;
  margin-top: 4rpx;
}

.privacy-title {
  display: block;
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
}

.privacy-desc {
  display: block;
  font-size: 24rpx;
  color: #666;
  line-height: 1.5;
}

/* 创建按钮 */
.create-btn {
  width: 100%;
  padding: 32rpx;
  border-radius: 24rpx;
  font-size: 36rpx;
  font-weight: 600;
  border: none;
  box-shadow: 0 8rpx 40rpx rgba(102, 126, 234, 0.3);
}

.create-btn.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.create-btn.disabled {
  background: #ddd;
  color: #999;
  box-shadow: none;
}
