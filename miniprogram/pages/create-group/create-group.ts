// create-group.ts

const iconOptions = [
  { icon: '🏃', color: 'blue' },
  { icon: '📚', color: 'purple' },
  { icon: '💪', color: 'green' },
  { icon: '🎵', color: 'yellow' },
  { icon: '🎨', color: 'red' },
  { icon: '💻', color: 'indigo' },
  { icon: '❤️', color: 'pink' },
  { icon: '🌱', color: 'teal' },
  { icon: '📷', color: 'orange' },
  { icon: '➕', color: 'gray' }
]

Component({
  data: {
    iconOptions,
    selectedIcon: '🏃',
    selectedIconColor: 'blue',
    groupName: '',
    groupDescription: '',
    targetCount: 22,
    enableReminder: true,
    isPublic: true,
    creating: false,
    canCreate: false
  },

  lifetimes: {
    attached() {
      this.checkCanCreate()
    }
  },

  methods: {
    // 选择图标
    selectIcon(e: any) {
      const { icon, color } = e.currentTarget.dataset
      this.setData({
        selectedIcon: icon,
        selectedIconColor: color
      })
    },

    // 小组名称输入
    onGroupNameInput(e: any) {
      this.setData({
        groupName: e.detail.value
      }, () => {
        this.checkCanCreate()
      })
    },

    // 小组描述输入
    onGroupDescriptionInput(e: any) {
      this.setData({
        groupDescription: e.detail.value
      })
    },

    // 减少目标次数
    decreaseTarget() {
      if (this.data.targetCount > 1) {
        this.setData({
          targetCount: this.data.targetCount - 1
        })
      }
    },

    // 增加目标次数
    increaseTarget() {
      if (this.data.targetCount < 31) {
        this.setData({
          targetCount: this.data.targetCount + 1
        })
      }
    },

    // 提醒设置变化
    onReminderChange(e: any) {
      this.setData({
        enableReminder: e.detail.value
      })
    },

    // 设置隐私
    setPrivacy(e: any) {
      const isPublic = e.currentTarget.dataset.public
      this.setData({
        isPublic
      })
    },

    // 检查是否可以创建
    checkCanCreate() {
      const { groupName } = this.data
      const canCreate = groupName.trim().length > 0
      
      this.setData({
        canCreate
      })
    },

    // 创建小组
    async createGroup() {
      if (!this.data.canCreate || this.data.creating) {
        return
      }

      // 验证输入
      if (this.data.groupName.trim().length === 0) {
        wx.showToast({
          title: '请输入小组名称',
          icon: 'none'
        })
        return
      }

      this.setData({
        creating: true
      })

      try {
        // 模拟创建小组
        await this.submitCreateGroup()
        
        wx.showToast({
          title: '小组创建成功！',
          icon: 'success'
        })

        // 延迟跳转到小组详情
        setTimeout(() => {
          const groupId = this.generateGroupId()
          wx.redirectTo({
            url: `/pages/group-detail/group-detail?id=${groupId}`
          })
        }, 1500)

      } catch (error) {
        console.error('创建小组失败:', error)
        wx.showToast({
          title: '创建失败，请重试',
          icon: 'none'
        })
      } finally {
        this.setData({
          creating: false
        })
      }
    },

    // 模拟提交创建小组
    submitCreateGroup(): Promise<void> {
      return new Promise((resolve) => {
        // 模拟网络请求延迟
        setTimeout(() => {
          // 保存小组信息到本地存储
          this.saveGroupToLocal()
          resolve()
        }, 2000)
      })
    },

    // 保存小组到本地存储
    saveGroupToLocal() {
      const groupId = this.generateGroupId()
      const groupData = {
        id: groupId,
        name: this.data.groupName,
        description: this.data.groupDescription,
        icon: this.data.selectedIcon,
        iconColor: this.data.selectedIconColor,
        targetCount: this.data.targetCount,
        enableReminder: this.data.enableReminder,
        isPublic: this.data.isPublic,
        role: '管理员',
        memberCount: 1,
        currentCount: 0,
        createdAt: Date.now()
      }

      // 获取现有小组列表
      const groups = wx.getStorageSync('user_groups') || []
      groups.unshift(groupData)
      wx.setStorageSync('user_groups', groups)
    },

    // 生成小组ID
    generateGroupId(): string {
      return Date.now().toString()
    }
  }
})
