// home.ts
import { formatTime } from '../../utils/util'

// Mock数据
const mockGroups = [
  {
    id: 1,
    name: '晨跑俱乐部',
    icon: '🏃',
    iconColor: 'blue',
    currentCount: 18,
    targetCount: 22,
    status: 'completed',
    statusIcon: '✓'
  },
  {
    id: 2,
    name: '读书分享会',
    icon: '📚',
    iconColor: 'purple',
    currentCount: 15,
    targetCount: 20,
    status: 'pending',
    statusIcon: '⏰'
  },
  {
    id: 3,
    name: '健身打卡',
    icon: '💪',
    iconColor: 'green',
    currentCount: 12,
    targetCount: 25,
    status: 'warning',
    statusIcon: '!'
  }
]

Component({
  data: {
    greetingText: '早上好！',
    todayDate: '',
    todayChecked: false,
    myGroups: mockGroups,
    monthStats: {
      totalCheckins: 33,
      consecutiveDays: 7,
      completionRate: 85
    }
  },

  lifetimes: {
    attached() {
      this.initPage()
    }
  },

  methods: {
    initPage() {
      this.setGreeting()
      this.setTodayDate()
      this.checkTodayStatus()
    },

    setGreeting() {
      const hour = new Date().getHours()
      let greeting = '早上好！'
      
      if (hour >= 6 && hour < 12) {
        greeting = '早上好！'
      } else if (hour >= 12 && hour < 18) {
        greeting = '下午好！'
      } else {
        greeting = '晚上好！'
      }
      
      this.setData({
        greetingText: greeting
      })
    },

    setTodayDate() {
      const today = new Date()
      const year = today.getFullYear()
      const month = today.getMonth() + 1
      const day = today.getDate()
      
      this.setData({
        todayDate: `${year}年${month}月${day}日`
      })
    },

    checkTodayStatus() {
      // 检查今日是否已打卡（从本地存储或服务器获取）
      const todayKey = this.getTodayKey()
      const checkedToday = wx.getStorageSync(`checkin_${todayKey}`) || false
      
      this.setData({
        todayChecked: checkedToday
      })
    },

    getTodayKey() {
      const today = new Date()
      return `${today.getFullYear()}-${today.getMonth() + 1}-${today.getDate()}`
    },

    // 页面跳转方法
    goToCheckin() {
      if (this.data.todayChecked) {
        wx.showToast({
          title: '今日已打卡',
          icon: 'success'
        })
        return
      }
      
      wx.navigateTo({
        url: '/pages/checkin/checkin'
      })
    },

    goToGroups() {
      wx.switchTab({
        url: '/pages/groups/groups'
      })
    },

    goToGroupDetail(e: any) {
      const groupId = e.currentTarget.dataset.id
      wx.navigateTo({
        url: `/pages/group-detail/group-detail?id=${groupId}`
      })
    },

    // 页面显示时刷新数据
    onShow() {
      this.checkTodayStatus()
    }
  }
})
