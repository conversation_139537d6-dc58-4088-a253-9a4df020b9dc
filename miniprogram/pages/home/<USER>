/* home.wxss */
.container {
  padding: 32rpx;
  background-color: #f6f6f6;
  min-height: 100vh;
}

/* 头部问候 */
.greeting {
  margin-bottom: 48rpx;
}

.greeting-title {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.greeting-subtitle {
  display: block;
  font-size: 28rpx;
  color: #666;
}

/* 今日打卡卡片 */
.checkin-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 32rpx;
  padding: 48rpx;
  margin-bottom: 48rpx;
  color: white;
  box-shadow: 0 8rpx 40rpx rgba(102, 126, 234, 0.3);
}

.checkin-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32rpx;
}

.checkin-title {
  display: block;
  font-size: 36rpx;
  font-weight: 600;
  margin-bottom: 8rpx;
}

.checkin-date {
  display: block;
  font-size: 24rpx;
  opacity: 0.8;
}

.checkin-icon {
  width: 96rpx;
  height: 96rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 48rpx;
}

.checkin-icon.checked {
  background: rgba(255, 255, 255, 0.3);
}

.checkin-btn {
  width: 100%;
  background: rgba(255, 255, 255, 0.2);
  border: none;
  border-radius: 24rpx;
  padding: 24rpx;
  color: white;
  font-size: 32rpx;
  font-weight: 500;
}

.checkin-btn:disabled {
  opacity: 0.6;
}

/* 我的小组 */
.my-groups {
  margin-bottom: 48rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32rpx;
}

.section-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.section-more {
  font-size: 28rpx;
  color: #667eea;
}

.groups-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.group-item {
  background: white;
  border-radius: 24rpx;
  padding: 32rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 8rpx 40rpx rgba(0, 0, 0, 0.08);
}

.group-content {
  display: flex;
  align-items: center;
}

.group-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
  font-size: 32rpx;
}

.group-icon.blue {
  background: #e3f2fd;
  color: #1976d2;
}

.group-icon.purple {
  background: #f3e5f5;
  color: #7b1fa2;
}

.group-icon.green {
  background: #e8f5e8;
  color: #388e3c;
}

.group-name {
  display: block;
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
}

.group-progress {
  display: block;
  font-size: 24rpx;
  color: #666;
}

.group-status {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
}

.group-status.completed {
  background: #e8f5e8;
  color: #4caf50;
}

.group-status.pending {
  background: #fff3e0;
  color: #ff9800;
}

.group-status.warning {
  background: #ffebee;
  color: #f44336;
}

/* 统计概览 */
.stats-overview {
  background: white;
  border-radius: 24rpx;
  padding: 40rpx;
  box-shadow: 0 8rpx 40rpx rgba(0, 0, 0, 0.08);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 32rpx;
  margin-top: 24rpx;
}

.stat-item {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.stat-item:nth-child(1) .stat-number {
  color: #667eea;
}

.stat-item:nth-child(2) .stat-number {
  color: #4caf50;
}

.stat-item:nth-child(3) .stat-number {
  color: #9c27b0;
}

.stat-label {
  display: block;
  font-size: 24rpx;
  color: #666;
}
