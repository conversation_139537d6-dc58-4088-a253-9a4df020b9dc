// checkin.ts

// Mock数据
const mockGroups = [
  {
    id: 1,
    name: '晨跑俱乐部',
    icon: '🏃',
    iconColor: 'blue'
  },
  {
    id: 2,
    name: '读书分享会',
    icon: '📚',
    iconColor: 'purple'
  },
  {
    id: 3,
    name: '健身打卡',
    icon: '💪',
    iconColor: 'green'
  }
]

Component({
  data: {
    todayNumber: '',
    todayText: '',
    imageUrl: '',
    noteText: '',
    selectedGroupId: 1,
    groupList: mockGroups,
    submitting: false,
    canSubmit: false
  },

  lifetimes: {
    attached() {
      this.initPage()
    }
  },

  methods: {
    initPage() {
      this.setDateInfo()
      this.checkCanSubmit()
    },

    setDateInfo() {
      const today = new Date()
      const day = today.getDate()
      const year = today.getFullYear()
      const month = today.getMonth() + 1
      const weekDay = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'][today.getDay()]
      
      this.setData({
        todayNumber: day.toString(),
        todayText: `${year}年${month}月 · ${weekDay}`
      })
    },

    // 拍照
    takePhoto() {
      wx.chooseMedia({
        count: 1,
        mediaType: ['image'],
        sourceType: ['camera'],
        maxDuration: 30,
        camera: 'back',
        success: (res) => {
          this.handleImageSuccess(res.tempFiles[0])
        },
        fail: (err) => {
          console.error('拍照失败:', err)
        }
      })
    },

    // 从相册选择
    chooseFromAlbum() {
      wx.chooseMedia({
        count: 1,
        mediaType: ['image'],
        sourceType: ['album'],
        success: (res) => {
          this.handleImageSuccess(res.tempFiles[0])
        },
        fail: (err) => {
          console.error('选择图片失败:', err)
        }
      })
    },

    // 选择图片（通用）
    chooseImage() {
      wx.showActionSheet({
        itemList: ['拍照', '从相册选择'],
        success: (res) => {
          if (res.tapIndex === 0) {
            this.takePhoto()
          } else if (res.tapIndex === 1) {
            this.chooseFromAlbum()
          }
        }
      })
    },

    // 处理图片选择成功
    handleImageSuccess(file: any) {
      // 检查文件大小（5MB限制）
      if (file.size > 5 * 1024 * 1024) {
        wx.showToast({
          title: '图片大小不能超过5MB',
          icon: 'none'
        })
        return
      }

      this.setData({
        imageUrl: file.tempFilePath
      }, () => {
        this.checkCanSubmit()
      })
    },

    // 删除图片
    deleteImage() {
      this.setData({
        imageUrl: ''
      }, () => {
        this.checkCanSubmit()
      })
    },

    // 备注输入
    onNoteInput(e: any) {
      this.setData({
        noteText: e.detail.value
      })
    },

    // 选择小组
    selectGroup(e: any) {
      const groupId = e.currentTarget.dataset.id
      this.setData({
        selectedGroupId: groupId
      }, () => {
        this.checkCanSubmit()
      })
    },

    // 检查是否可以提交
    checkCanSubmit() {
      const { imageUrl, selectedGroupId } = this.data
      const canSubmit = imageUrl && selectedGroupId
      
      this.setData({
        canSubmit
      })
    },

    // 提交打卡
    async submitCheckin() {
      if (!this.data.canSubmit || this.data.submitting) {
        return
      }

      this.setData({
        submitting: true
      })

      try {
        // 模拟上传图片和提交数据
        await this.uploadImageAndSubmit()
        
        // 保存打卡记录到本地
        this.saveCheckinRecord()
        
        wx.showToast({
          title: '打卡成功！',
          icon: 'success'
        })

        // 延迟返回首页
        setTimeout(() => {
          wx.navigateBack()
        }, 1500)

      } catch (error) {
        console.error('打卡失败:', error)
        wx.showToast({
          title: '打卡失败，请重试',
          icon: 'none'
        })
      } finally {
        this.setData({
          submitting: false
        })
      }
    },

    // 模拟上传图片和提交数据
    uploadImageAndSubmit(): Promise<void> {
      return new Promise((resolve) => {
        // 模拟网络请求延迟
        setTimeout(() => {
          resolve()
        }, 2000)
      })
    },

    // 保存打卡记录到本地存储
    saveCheckinRecord() {
      const today = new Date()
      const todayKey = `${today.getFullYear()}-${today.getMonth() + 1}-${today.getDate()}`
      
      const checkinData = {
        date: todayKey,
        imageUrl: this.data.imageUrl,
        note: this.data.noteText,
        groupId: this.data.selectedGroupId,
        timestamp: Date.now()
      }

      // 保存今日打卡状态
      wx.setStorageSync(`checkin_${todayKey}`, true)
      
      // 保存打卡详情
      wx.setStorageSync(`checkin_detail_${todayKey}`, checkinData)
      
      // 更新打卡历史记录
      const history = wx.getStorageSync('checkin_history') || []
      history.unshift(checkinData)
      wx.setStorageSync('checkin_history', history)
    }
  }
})
