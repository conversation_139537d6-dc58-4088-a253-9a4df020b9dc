<!--groups.wxml-->
<view class="container">
  <!-- 快速操作 -->
  <view class="quick-actions">
    <button class="action-btn primary" bindtap="createGroup">
      ➕ 创建小组
    </button>
    <button class="action-btn secondary" bindtap="joinGroup">
      🔍 加入小组
    </button>
  </view>

  <!-- 小组列表 -->
  <view class="groups-list">
    <view 
      class="group-card" 
      wx:for="{{groupList}}" 
      wx:key="id"
      bindtap="goToGroupDetail"
      data-id="{{item.id}}"
    >
      <!-- 小组头部 -->
      <view class="group-header">
        <view class="group-info">
          <view class="group-icon {{item.iconColor}}">
            <text>{{item.icon}}</text>
          </view>
          <view class="group-details">
            <text class="group-name">{{item.name}}</text>
            <text class="group-meta">{{item.memberCount}}名成员 · {{item.role}}</text>
          </view>
        </view>
        <view class="group-status {{item.status}}">
          <text>{{item.statusIcon}}</text>
        </view>
      </view>

      <!-- 进度条 -->
      <view class="progress-section">
        <view class="progress-info">
          <text class="progress-label">本月进度</text>
          <text class="progress-text">{{item.currentCount}}/{{item.targetCount}}</text>
        </view>
        <view class="progress-bar">
          <view 
            class="progress-fill {{item.progressColor}}" 
            style="width: {{item.progressPercent}}%"
          ></view>
        </view>
      </view>

      <!-- 成员头像 -->
      <view class="members-section">
        <view class="member-avatars">
          <image 
            class="member-avatar" 
            wx:for="{{item.recentMembers}}" 
            wx:key="id"
            wx:for-item="member"
            src="{{member.avatar}}"
          ></image>
          <view class="more-members" wx:if="{{item.memberCount > item.recentMembers.length}}">
            +{{item.memberCount - item.recentMembers.length}}
          </view>
        </view>
        <text class="detail-link">查看详情</text>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{groupList.length === 0}}">
    <view class="empty-icon">📝</view>
    <text class="empty-title">还没有加入任何小组</text>
    <text class="empty-subtitle">创建或加入一个小组开始打卡吧</text>
    <button class="empty-action" bindtap="createGroup">创建小组</button>
  </view>
</view>
