// groups.ts

// Mock数据
const mockGroups = [
  {
    id: 1,
    name: '晨跑俱乐部',
    icon: '🏃',
    iconColor: 'blue',
    memberCount: 32,
    role: '管理员',
    currentCount: 18,
    targetCount: 22,
    status: 'completed',
    statusIcon: '✓',
    progressPercent: 82,
    progressColor: 'green',
    recentMembers: [
      { id: 1, avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=64&h=64&fit=crop&crop=face' },
      { id: 2, avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=64&h=64&fit=crop&crop=face' },
      { id: 3, avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=64&h=64&fit=crop&crop=face' }
    ]
  },
  {
    id: 2,
    name: '读书分享会',
    icon: '📚',
    iconColor: 'purple',
    memberCount: 18,
    role: '普通成员',
    currentCount: 15,
    targetCount: 20,
    status: 'pending',
    statusIcon: '⏰',
    progressPercent: 75,
    progressColor: 'yellow',
    recentMembers: [
      { id: 4, avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=64&h=64&fit=crop&crop=face' },
      { id: 5, avatar: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=64&h=64&fit=crop&crop=face' }
    ]
  },
  {
    id: 3,
    name: '健身打卡',
    icon: '💪',
    iconColor: 'green',
    memberCount: 45,
    role: '普通成员',
    currentCount: 12,
    targetCount: 25,
    status: 'warning',
    statusIcon: '!',
    progressPercent: 48,
    progressColor: 'red',
    recentMembers: [
      { id: 6, avatar: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=64&h=64&fit=crop&crop=face' },
      { id: 7, avatar: 'https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=64&h=64&fit=crop&crop=face' },
      { id: 8, avatar: 'https://images.unsplash.com/photo-1517841905240-472988babdf9?w=64&h=64&fit=crop&crop=face' }
    ]
  }
]

Component({
  data: {
    groupList: mockGroups
  },

  lifetimes: {
    attached() {
      this.loadGroups()
    }
  },

  methods: {
    // 加载小组列表
    loadGroups() {
      // 这里可以从本地存储或服务器加载数据
      // 目前使用mock数据
      this.setData({
        groupList: mockGroups
      })
    },

    // 创建小组
    createGroup() {
      wx.navigateTo({
        url: '/pages/create-group/create-group'
      })
    },

    // 加入小组
    joinGroup() {
      wx.navigateTo({
        url: '/pages/join-group/join-group'
      })
    },

    // 查看小组详情
    goToGroupDetail(e: any) {
      const groupId = e.currentTarget.dataset.id
      wx.navigateTo({
        url: `/pages/group-detail/group-detail?id=${groupId}`
      })
    },

    // 页面显示时刷新数据
    onShow() {
      this.loadGroups()
    }
  }
})
