// profile.ts

const defaultAvatarUrl = 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=160&h=160&fit=crop&crop=face'

Component({
  data: {
    userInfo: {
      avatarUrl: defaultAvatarUrl,
      nickName: '张小美',
      userId: '123456',
      joinDate: '2024.01.15'
    },
    userStats: {
      totalCheckins: 128,
      joinedGroups: 3,
      maxConsecutive: 15
    },
    unreadCount: 3,
    settings: {
      pushNotification: true,
      darkMode: false
    }
  },

  lifetimes: {
    attached() {
      this.loadUserInfo()
      this.loadUserStats()
      this.loadSettings()
    }
  },

  methods: {
    // 加载用户信息
    loadUserInfo() {
      // 从本地存储或服务器获取用户信息
      const userInfo = wx.getStorageSync('userInfo')
      if (userInfo) {
        this.setData({
          userInfo: {
            ...this.data.userInfo,
            ...userInfo
          }
        })
      }
    },

    // 加载用户统计数据
    loadUserStats() {
      // 从本地存储计算统计数据
      const history = wx.getStorageSync('checkin_history') || []
      const totalCheckins = history.length
      const maxConsecutive = this.calculateMaxConsecutive(history)
      
      this.setData({
        userStats: {
          totalCheckins,
          joinedGroups: 3, // 从小组数据获取
          maxConsecutive
        }
      })
    },

    // 计算最长连续打卡天数
    calculateMaxConsecutive(history: any[]) {
      if (history.length === 0) return 0
      
      let maxConsecutive = 0
      let currentConsecutive = 0
      
      // 按日期排序
      const sortedHistory = history.sort((a, b) => 
        new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
      )
      
      for (let i = 0; i < sortedHistory.length; i++) {
        if (i === 0) {
          currentConsecutive = 1
        } else {
          const prevDate = new Date(sortedHistory[i - 1].timestamp)
          const currentDate = new Date(sortedHistory[i].timestamp)
          const diffDays = Math.floor((currentDate.getTime() - prevDate.getTime()) / (1000 * 60 * 60 * 24))
          
          if (diffDays === 1) {
            currentConsecutive++
          } else {
            maxConsecutive = Math.max(maxConsecutive, currentConsecutive)
            currentConsecutive = 1
          }
        }
      }
      
      return Math.max(maxConsecutive, currentConsecutive)
    },

    // 加载设置
    loadSettings() {
      const settings = wx.getStorageSync('settings') || {}
      this.setData({
        settings: {
          ...this.data.settings,
          ...settings
        }
      })
    },

    // 推送通知设置变化
    onPushNotificationChange(e: any) {
      const value = e.detail.value
      this.setData({
        'settings.pushNotification': value
      })
      
      // 保存到本地存储
      const settings = wx.getStorageSync('settings') || {}
      settings.pushNotification = value
      wx.setStorageSync('settings', settings)
    },

    // 夜间模式设置变化
    onDarkModeChange(e: any) {
      const value = e.detail.value
      this.setData({
        'settings.darkMode': value
      })
      
      // 保存到本地存储
      const settings = wx.getStorageSync('settings') || {}
      settings.darkMode = value
      wx.setStorageSync('settings', settings)
    },

    // 页面跳转方法
    goToCheckinHistory() {
      wx.navigateTo({
        url: '/pages/checkin-history/checkin-history'
      })
    },

    goToAchievements() {
      wx.showToast({
        title: '功能开发中',
        icon: 'none'
      })
    },

    goToMyGroups() {
      wx.switchTab({
        url: '/pages/groups/groups'
      })
    },

    goToNotifications() {
      wx.navigateTo({
        url: '/pages/notifications/notifications'
      })
    },

    goToPrivacySettings() {
      wx.showToast({
        title: '功能开发中',
        icon: 'none'
      })
    },

    goToHelp() {
      wx.showToast({
        title: '功能开发中',
        icon: 'none'
      })
    },

    goToAbout() {
      wx.showToast({
        title: '功能开发中',
        icon: 'none'
      })
    },

    // 退出登录
    logout() {
      wx.showModal({
        title: '确认退出',
        content: '确定要退出登录吗？',
        success: (res) => {
          if (res.confirm) {
            // 清除本地存储的用户数据
            wx.removeStorageSync('userInfo')
            wx.removeStorageSync('checkin_history')
            wx.removeStorageSync('settings')
            
            wx.showToast({
              title: '已退出登录',
              icon: 'success'
            })
            
            // 重新启动小程序
            setTimeout(() => {
              wx.reLaunch({
                url: '/pages/home/<USER>'
              })
            }, 1500)
          }
        }
      })
    },

    // 页面显示时刷新数据
    onShow() {
      this.loadUserStats()
    }
  }
})
