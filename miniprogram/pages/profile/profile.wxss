/* profile.wxss */
.container {
  padding: 32rpx;
  background-color: #f6f6f6;
  min-height: 100vh;
}

/* 用户信息卡片 */
.user-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 32rpx;
  padding: 48rpx;
  margin-bottom: 48rpx;
  color: white;
  box-shadow: 0 8rpx 40rpx rgba(102, 126, 234, 0.3);
}

.user-info {
  display: flex;
  align-items: center;
  margin-bottom: 32rpx;
}

.user-avatar {
  width: 160rpx;
  height: 160rpx;
  border-radius: 50%;
  border: 8rpx solid rgba(255, 255, 255, 0.2);
  margin-right: 32rpx;
}

.user-name {
  display: block;
  font-size: 40rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.user-id {
  display: block;
  font-size: 24rpx;
  opacity: 0.8;
  margin-bottom: 4rpx;
}

.join-date {
  display: block;
  font-size: 24rpx;
  opacity: 0.8;
}

.user-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 32rpx;
}

.stat-item {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.stat-label {
  display: block;
  font-size: 24rpx;
  opacity: 0.8;
}

/* 功能菜单 */
.menu-section {
  background: white;
  border-radius: 32rpx;
  padding: 40rpx;
  margin-bottom: 48rpx;
  box-shadow: 0 8rpx 40rpx rgba(0, 0, 0, 0.08);
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
  font-size: 32rpx;
}

.menu-icon.blue {
  background: #e3f2fd;
  color: #1976d2;
}

.menu-icon.green {
  background: #e8f5e8;
  color: #388e3c;
}

.menu-icon.purple {
  background: #f3e5f5;
  color: #7b1fa2;
}

.menu-icon.yellow {
  background: #fff3e0;
  color: #f57c00;
}

.menu-text {
  flex: 1;
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

.menu-content {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.notification-badge {
  background: #f44336;
  color: white;
  border-radius: 50%;
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20rpx;
  font-weight: bold;
}

.menu-arrow {
  font-size: 32rpx;
  color: #ccc;
}

/* 设置选项 */
.settings-section {
  background: white;
  border-radius: 32rpx;
  padding: 40rpx;
  margin-bottom: 48rpx;
  box-shadow: 0 8rpx 40rpx rgba(0, 0, 0, 0.08);
}

.section-title {
  display: block;
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 32rpx;
}

.setting-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.setting-item:last-child {
  border-bottom: none;
}

.setting-info {
  display: flex;
  align-items: center;
}

.setting-icon {
  font-size: 32rpx;
  margin-right: 24rpx;
}

.setting-text {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

/* 退出登录按钮 */
.logout-btn {
  width: 100%;
  background: #ffebee;
  color: #f44336;
  border: 2rpx solid #ffcdd2;
  border-radius: 24rpx;
  padding: 32rpx;
  font-size: 32rpx;
  font-weight: 600;
}
