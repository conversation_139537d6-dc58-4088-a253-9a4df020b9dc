<!--profile.wxml-->
<view class="container">
  <!-- 用户信息卡片 -->
  <view class="user-card">
    <view class="user-info">
      <image class="user-avatar" src="{{userInfo.avatarUrl}}" mode="aspectFill"></image>
      <view class="user-details">
        <text class="user-name">{{userInfo.nickName}}</text>
        <text class="user-id">ID: {{userInfo.userId}}</text>
        <text class="join-date">加入时间: {{userInfo.joinDate}}</text>
      </view>
    </view>
    <view class="user-stats">
      <view class="stat-item">
        <text class="stat-number">{{userStats.totalCheckins}}</text>
        <text class="stat-label">总打卡</text>
      </view>
      <view class="stat-item">
        <text class="stat-number">{{userStats.joinedGroups}}</text>
        <text class="stat-label">参与小组</text>
      </view>
      <view class="stat-item">
        <text class="stat-number">{{userStats.maxConsecutive}}</text>
        <text class="stat-label">最长连续</text>
      </view>
    </view>
  </view>

  <!-- 功能菜单 -->
  <view class="menu-section">
    <view class="menu-item" bindtap="goToCheckinHistory">
      <view class="menu-icon blue">
        <text>📅</text>
      </view>
      <text class="menu-text">我的打卡记录</text>
      <text class="menu-arrow">></text>
    </view>

    <view class="menu-item" bindtap="goToAchievements">
      <view class="menu-icon green">
        <text>🏆</text>
      </view>
      <text class="menu-text">我的成就</text>
      <text class="menu-arrow">></text>
    </view>

    <view class="menu-item" bindtap="goToMyGroups">
      <view class="menu-icon purple">
        <text>👥</text>
      </view>
      <text class="menu-text">我的小组</text>
      <text class="menu-arrow">></text>
    </view>

    <view class="menu-item" bindtap="goToNotifications">
      <view class="menu-icon yellow">
        <text>🔔</text>
      </view>
      <view class="menu-content">
        <text class="menu-text">消息通知</text>
        <view class="notification-badge" wx:if="{{unreadCount > 0}}">
          <text>{{unreadCount}}</text>
        </view>
      </view>
      <text class="menu-arrow">></text>
    </view>
  </view>

  <!-- 设置选项 -->
  <view class="settings-section">
    <text class="section-title">设置</text>
    
    <view class="setting-item">
      <view class="setting-info">
        <text class="setting-icon">🔔</text>
        <text class="setting-text">推送通知</text>
      </view>
      <switch 
        checked="{{settings.pushNotification}}" 
        bindchange="onPushNotificationChange"
        color="#667eea"
      />
    </view>

    <view class="setting-item">
      <view class="setting-info">
        <text class="setting-icon">🌙</text>
        <text class="setting-text">夜间模式</text>
      </view>
      <switch 
        checked="{{settings.darkMode}}" 
        bindchange="onDarkModeChange"
        color="#667eea"
      />
    </view>

    <view class="setting-item" bindtap="goToPrivacySettings">
      <view class="setting-info">
        <text class="setting-icon">🛡️</text>
        <text class="setting-text">隐私设置</text>
      </view>
      <text class="menu-arrow">></text>
    </view>

    <view class="setting-item" bindtap="goToHelp">
      <view class="setting-info">
        <text class="setting-icon">❓</text>
        <text class="setting-text">帮助与反馈</text>
      </view>
      <text class="menu-arrow">></text>
    </view>

    <view class="setting-item" bindtap="goToAbout">
      <view class="setting-info">
        <text class="setting-icon">ℹ️</text>
        <text class="setting-text">关于我们</text>
      </view>
      <text class="menu-arrow">></text>
    </view>
  </view>

  <!-- 退出登录 -->
  <button class="logout-btn" bindtap="logout">
    退出登录
  </button>
</view>
