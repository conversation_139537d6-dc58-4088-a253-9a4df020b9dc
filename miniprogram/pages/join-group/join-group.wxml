<!--join-group.wxml-->
<view class="container">
  <!-- 加入方式选择 -->
  <view class="join-methods">
    <button 
      class="method-btn {{currentMethod === 'code' ? 'active' : 'inactive'}}" 
      bindtap="switchMethod"
      data-method="code"
    >
      <view class="method-icon">⌨️</view>
      <text class="method-text">输入邀请码</text>
    </button>
    <button 
      class="method-btn {{currentMethod === 'scan' ? 'active' : 'inactive'}}" 
      bindtap="switchMethod"
      data-method="scan"
    >
      <view class="method-icon">📱</view>
      <text class="method-text">扫描二维码</text>
    </button>
  </view>

  <!-- 输入邀请码 -->
  <view class="input-section" wx:if="{{currentMethod === 'code'}}">
    <text class="section-label">输入邀请码</text>
    <view class="code-input-container">
      <input 
        class="code-input"
        placeholder="请输入6位邀请码"
        value="{{inviteCode}}"
        bindinput="onCodeInput"
        maxlength="6"
        type="text"
      />
      <button 
        class="search-btn {{inviteCode.length === 6 ? 'active' : 'disabled'}}" 
        bindtap="searchGroup"
        disabled="{{inviteCode.length !== 6}}"
      >
        搜索
      </button>
    </view>
  </view>

  <!-- 扫描二维码 -->
  <view class="scan-section" wx:if="{{currentMethod === 'scan'}}">
    <text class="section-label">扫描二维码</text>
    <view class="scan-area" bindtap="scanQRCode">
      <view class="scan-icon">📷</view>
      <text class="scan-text">点击扫描小组二维码</text>
      <text class="scan-hint">将二维码放入框内即可自动扫描</text>
    </view>
  </view>

  <!-- 小组预览 -->
  <view class="group-preview" wx:if="{{foundGroup}}">
    <view class="preview-header">
      <view class="group-icon {{foundGroup.iconColor}}">
        <text>{{foundGroup.icon}}</text>
      </view>
      <view class="group-info">
        <text class="group-name">{{foundGroup.name}}</text>
        <text class="group-description">{{foundGroup.description}}</text>
      </view>
    </view>

    <view class="group-stats">
      <view class="stat-item">
        <text class="stat-number">{{foundGroup.memberCount}}</text>
        <text class="stat-label">成员数</text>
      </view>
      <view class="stat-item">
        <text class="stat-number">{{foundGroup.targetCount}}</text>
        <text class="stat-label">月目标</text>
      </view>
      <view class="stat-item">
        <text class="stat-number">{{foundGroup.completionRate}}%</text>
        <text class="stat-label">完成率</text>
      </view>
    </view>

    <view class="members-preview">
      <view class="members-info">
        <text class="members-label">活跃成员</text>
      </view>
      <view class="member-avatars">
        <image 
          class="member-avatar" 
          wx:for="{{foundGroup.recentMembers}}" 
          wx:key="id"
          wx:for-item="member"
          src="{{member.avatar}}"
          mode="aspectFill"
        ></image>
        <view class="more-members" wx:if="{{foundGroup.memberCount > foundGroup.recentMembers.length}}">
          +{{foundGroup.memberCount - foundGroup.recentMembers.length}}
        </view>
      </view>
    </view>

    <button 
      class="join-btn {{joining ? 'loading' : 'active'}}" 
      bindtap="joinGroup"
      disabled="{{joining}}"
    >
      {{joining ? '加入中...' : '申请加入'}}
    </button>
  </view>

  <!-- 推荐小组 -->
  <view class="recommended-section" wx:if="{{!foundGroup}}">
    <text class="section-title">推荐小组</text>
    <view class="recommended-list">
      <view 
        class="recommended-item" 
        wx:for="{{recommendedGroups}}" 
        wx:key="id"
        bindtap="previewGroup"
        data-group="{{item}}"
      >
        <view class="group-icon {{item.iconColor}}">
          <text>{{item.icon}}</text>
        </view>
        <view class="group-details">
          <text class="group-name">{{item.name}}</text>
          <text class="group-meta">{{item.memberCount}}名成员</text>
        </view>
        <button class="quick-join-btn" bindtap="quickJoin" data-id="{{item.id}}">
          加入
        </button>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{searchAttempted && !foundGroup}}">
    <view class="empty-icon">🔍</view>
    <text class="empty-title">未找到小组</text>
    <text class="empty-subtitle">请检查邀请码是否正确</text>
  </view>
</view>
