// join-group.ts

// Mock推荐小组数据
const mockRecommendedGroups = [
  {
    id: 2,
    name: '读书分享会',
    icon: '📚',
    iconColor: 'purple',
    memberCount: 18
  },
  {
    id: 3,
    name: '健身打卡',
    icon: '💪',
    iconColor: 'green',
    memberCount: 45
  }
]

// Mock小组数据
const mockGroupData = {
  'ABC123': {
    id: 1,
    name: '晨跑俱乐部',
    icon: '🏃',
    iconColor: 'blue',
    description: '每天早上6点一起晨跑，养成健康生活习惯，欢迎大家积极参与！',
    memberCount: 32,
    targetCount: 22,
    completionRate: 85,
    recentMembers: [
      { id: 1, avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=64&h=64&fit=crop&crop=face' },
      { id: 2, avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=64&h=64&fit=crop&crop=face' },
      { id: 3, avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=64&h=64&fit=crop&crop=face' }
    ]
  }
}

Component({
  data: {
    currentMethod: 'code',
    inviteCode: '',
    foundGroup: null,
    searchAttempted: false,
    joining: false,
    recommendedGroups: mockRecommendedGroups
  },

  lifetimes: {
    attached() {
      this.loadRecommendedGroups()
    }
  },

  methods: {
    // 切换加入方式
    switchMethod(e: any) {
      const method = e.currentTarget.dataset.method
      this.setData({
        currentMethod: method,
        foundGroup: null,
        searchAttempted: false
      })
    },

    // 邀请码输入
    onCodeInput(e: any) {
      this.setData({
        inviteCode: e.detail.value.toUpperCase()
      })
    },

    // 搜索小组
    searchGroup() {
      if (this.data.inviteCode.length !== 6) {
        return
      }

      const foundGroup = mockGroupData[this.data.inviteCode as keyof typeof mockGroupData]
      
      this.setData({
        foundGroup: foundGroup || null,
        searchAttempted: true
      })

      if (!foundGroup) {
        wx.showToast({
          title: '未找到小组',
          icon: 'none'
        })
      }
    },

    // 扫描二维码
    scanQRCode() {
      wx.scanCode({
        success: (res) => {
          // 解析二维码内容
          const scannedData = res.result
          
          // 这里应该解析二维码中的小组信息
          // 目前模拟扫描到邀请码
          if (scannedData.includes('ABC123')) {
            this.setData({
              inviteCode: 'ABC123'
            })
            this.searchGroup()
          } else {
            wx.showToast({
              title: '无效的二维码',
              icon: 'none'
            })
          }
        },
        fail: (err) => {
          console.error('扫码失败:', err)
          wx.showToast({
            title: '扫码失败',
            icon: 'none'
          })
        }
      })
    },

    // 加载推荐小组
    loadRecommendedGroups() {
      // 这里可以从服务器获取推荐小组
      this.setData({
        recommendedGroups: mockRecommendedGroups
      })
    },

    // 预览小组
    previewGroup(e: any) {
      const group = e.currentTarget.dataset.group
      this.setData({
        foundGroup: {
          ...group,
          description: '这是一个很棒的小组，快来加入我们吧！',
          targetCount: 20,
          completionRate: 78,
          recentMembers: [
            { id: 1, avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=64&h=64&fit=crop&crop=face' },
            { id: 2, avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=64&h=64&fit=crop&crop=face' }
          ]
        }
      })
    },

    // 快速加入
    quickJoin(e: any) {
      e.stopPropagation()
      const groupId = e.currentTarget.dataset.id
      this.performJoin(groupId)
    },

    // 加入小组
    joinGroup() {
      if (!this.data.foundGroup || this.data.joining) {
        return
      }

      this.performJoin(this.data.foundGroup.id)
    },

    // 执行加入操作
    async performJoin(groupId: string) {
      this.setData({
        joining: true
      })

      try {
        // 模拟加入小组
        await this.submitJoinGroup(groupId)
        
        wx.showToast({
          title: '加入成功！',
          icon: 'success'
        })

        // 延迟跳转到小组详情
        setTimeout(() => {
          wx.redirectTo({
            url: `/pages/group-detail/group-detail?id=${groupId}`
          })
        }, 1500)

      } catch (error) {
        console.error('加入小组失败:', error)
        wx.showToast({
          title: '加入失败，请重试',
          icon: 'none'
        })
      } finally {
        this.setData({
          joining: false
        })
      }
    },

    // 模拟提交加入小组
    submitJoinGroup(groupId: string): Promise<void> {
      return new Promise((resolve) => {
        // 模拟网络请求延迟
        setTimeout(() => {
          // 保存加入的小组到本地存储
          this.saveJoinedGroup(groupId)
          resolve()
        }, 2000)
      })
    },

    // 保存加入的小组到本地存储
    saveJoinedGroup(groupId: string) {
      const foundGroup = this.data.foundGroup
      if (!foundGroup) return

      const groupData = {
        ...foundGroup,
        role: '普通成员',
        currentCount: 0,
        joinedAt: Date.now()
      }

      // 获取现有小组列表
      const groups = wx.getStorageSync('user_groups') || []
      
      // 检查是否已经加入
      const existingIndex = groups.findIndex((g: any) => g.id === groupId)
      if (existingIndex === -1) {
        groups.unshift(groupData)
        wx.setStorageSync('user_groups', groups)
      }
    }
  }
})
