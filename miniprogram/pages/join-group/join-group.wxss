/* join-group.wxss */
.container {
  padding: 32rpx;
  background-color: #f6f6f6;
  min-height: 100vh;
}

/* 加入方式选择 */
.join-methods {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 24rpx;
  margin-bottom: 48rpx;
}

.method-btn {
  padding: 32rpx 24rpx;
  border-radius: 24rpx;
  border: none;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
  font-size: 28rpx;
  font-weight: 500;
}

.method-btn.active {
  background: #667eea;
  color: white;
  box-shadow: 0 8rpx 40rpx rgba(102, 126, 234, 0.3);
}

.method-btn.inactive {
  background: white;
  color: #333;
  border: 2rpx solid #e0e0e0;
  box-shadow: 0 8rpx 40rpx rgba(0, 0, 0, 0.08);
}

.method-icon {
  font-size: 48rpx;
}

.method-text {
  font-size: 28rpx;
}

/* 输入区域 */
.input-section {
  margin-bottom: 48rpx;
}

.section-label {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 24rpx;
}

.code-input-container {
  display: flex;
  gap: 24rpx;
}

.code-input {
  flex: 1;
  background: white;
  border-radius: 24rpx;
  padding: 32rpx;
  font-size: 32rpx;
  text-align: center;
  letter-spacing: 8rpx;
  border: none;
  box-shadow: 0 8rpx 40rpx rgba(0, 0, 0, 0.08);
}

.search-btn {
  padding: 32rpx 48rpx;
  border-radius: 24rpx;
  font-size: 32rpx;
  font-weight: 500;
  border: none;
}

.search-btn.active {
  background: #667eea;
  color: white;
}

.search-btn.disabled {
  background: #ddd;
  color: #999;
}

/* 扫描区域 */
.scan-section {
  margin-bottom: 48rpx;
}

.scan-area {
  background: white;
  border: 4rpx dashed #ddd;
  border-radius: 24rpx;
  padding: 80rpx 32rpx;
  text-align: center;
  box-shadow: 0 8rpx 40rpx rgba(0, 0, 0, 0.08);
}

.scan-icon {
  font-size: 96rpx;
  margin-bottom: 24rpx;
}

.scan-text {
  display: block;
  font-size: 32rpx;
  color: #333;
  margin-bottom: 16rpx;
}

.scan-hint {
  display: block;
  font-size: 24rpx;
  color: #999;
}

/* 小组预览 */
.group-preview {
  background: white;
  border-radius: 32rpx;
  padding: 48rpx;
  margin-bottom: 48rpx;
  box-shadow: 0 8rpx 40rpx rgba(0, 0, 0, 0.08);
}

.preview-header {
  display: flex;
  align-items: center;
  margin-bottom: 32rpx;
}

.group-icon {
  width: 160rpx;
  height: 160rpx;
  border-radius: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 32rpx;
  font-size: 64rpx;
}

.group-icon.blue {
  background: #e3f2fd;
  color: #1976d2;
}

.group-icon.purple {
  background: #f3e5f5;
  color: #7b1fa2;
}

.group-icon.green {
  background: #e8f5e8;
  color: #388e3c;
}

.group-name {
  display: block;
  font-size: 40rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 16rpx;
}

.group-description {
  display: block;
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
}

.group-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 32rpx;
  margin-bottom: 32rpx;
}

.stat-item {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.stat-label {
  display: block;
  font-size: 24rpx;
  color: #666;
}

.members-preview {
  margin-bottom: 32rpx;
}

.members-info {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
}

.members-label {
  font-size: 28rpx;
  color: #666;
}

.member-avatars {
  display: flex;
  align-items: center;
}

.member-avatar {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  border: 4rpx solid white;
  margin-left: -16rpx;
}

.member-avatar:first-child {
  margin-left: 0;
}

.more-members {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  background: #f0f0f0;
  border: 4rpx solid white;
  margin-left: -16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20rpx;
  color: #666;
}

.join-btn {
  width: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 24rpx;
  padding: 32rpx;
  font-size: 36rpx;
  font-weight: 600;
  box-shadow: 0 8rpx 40rpx rgba(102, 126, 234, 0.3);
}

.join-btn.loading {
  opacity: 0.7;
}

/* 推荐小组 */
.recommended-section {
  margin-bottom: 48rpx;
}

.section-title {
  display: block;
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 32rpx;
}

.recommended-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.recommended-item {
  background: white;
  border-radius: 24rpx;
  padding: 32rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 8rpx 40rpx rgba(0, 0, 0, 0.08);
}

.recommended-item .group-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 16rpx;
  margin-right: 24rpx;
  font-size: 32rpx;
}

.group-details {
  flex: 1;
}

.recommended-item .group-name {
  font-size: 32rpx;
  margin-bottom: 8rpx;
}

.group-meta {
  font-size: 24rpx;
  color: #666;
}

.quick-join-btn {
  background: #667eea;
  color: white;
  border: none;
  border-radius: 16rpx;
  padding: 16rpx 32rpx;
  font-size: 28rpx;
  font-weight: 500;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 80rpx 40rpx;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 32rpx;
}

.empty-title {
  display: block;
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
}

.empty-subtitle {
  display: block;
  font-size: 28rpx;
  color: #666;
}
