<!--group-detail.wxml-->
<view class="container">
  <!-- 小组头部信息 -->
  <view class="group-header">
    <view class="group-info">
      <view class="group-icon {{groupInfo.iconColor}}">
        <text>{{groupInfo.icon}}</text>
      </view>
      <view class="group-details">
        <text class="group-name">{{groupInfo.name}}</text>
        <text class="group-meta">{{groupInfo.memberCount}}名成员 · {{groupInfo.role}}</text>
      </view>
    </view>
    <text class="group-description">{{groupInfo.description}}</text>
    <view class="group-actions" wx:if="{{groupInfo.role === '管理员'}}">
      <button class="action-btn invite" bindtap="inviteMembers">
        🔗 邀请成员
      </button>
      <button class="action-btn manage" bindtap="manageGroup">
        ⚙️ 管理设置
      </button>
    </view>
  </view>

  <!-- 本月统计 -->
  <view class="stats-card">
    <text class="card-title">本月统计</text>
    <view class="stats-grid">
      <view class="stat-item">
        <text class="stat-number">{{monthStats.targetDays}}</text>
        <text class="stat-label">目标天数</text>
      </view>
      <view class="stat-item">
        <text class="stat-number">{{monthStats.completedDays}}</text>
        <text class="stat-label">已完成</text>
      </view>
    </view>
    <view class="progress-section">
      <view class="progress-info">
        <text class="progress-label">整体完成率</text>
        <text class="progress-text">{{monthStats.completionRate}}%</text>
      </view>
      <view class="progress-bar">
        <view 
          class="progress-fill" 
          style="width: {{monthStats.completionRate}}%"
        ></view>
      </view>
    </view>
  </view>

  <!-- 成员列表 -->
  <view class="members-card">
    <view class="members-header">
      <text class="card-title">成员列表</text>
      <text class="member-count">{{groupInfo.memberCount}}人</text>
    </view>
    
    <view class="members-list">
      <view 
        class="member-item" 
        wx:for="{{memberList}}" 
        wx:key="id"
      >
        <view class="member-info">
          <image class="member-avatar" src="{{item.avatar}}" mode="aspectFill"></image>
          <view class="member-details">
            <view class="member-name-row">
              <text class="member-name">{{item.name}}</text>
              <view class="member-role {{item.role}}" wx:if="{{item.role === '管理员'}}">
                <text>管理员</text>
              </view>
            </view>
            <text class="member-progress">本月 {{item.currentCount}}/{{item.targetCount}} 次</text>
          </view>
        </view>
        <view class="member-status {{item.status}}">
          <text>{{item.statusIcon}}</text>
        </view>
      </view>
    </view>

    <button class="show-all-btn" bindtap="showAllMembers" wx:if="{{memberList.length < groupInfo.memberCount}}">
      查看全部成员
    </button>
  </view>

  <!-- 最近动态 -->
  <view class="activities-card">
    <text class="card-title">最近动态</text>
    <view class="activities-list">
      <view 
        class="activity-item" 
        wx:for="{{recentActivities}}" 
        wx:key="id"
      >
        <image class="activity-avatar" src="{{item.userAvatar}}" mode="aspectFill"></image>
        <view class="activity-content">
          <text class="activity-text">{{item.text}}</text>
          <text class="activity-time">{{item.time}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 退出小组按钮 -->
  <button class="leave-btn" bindtap="leaveGroup" wx:if="{{groupInfo.role !== '管理员'}}">
    退出小组
  </button>
</view>
