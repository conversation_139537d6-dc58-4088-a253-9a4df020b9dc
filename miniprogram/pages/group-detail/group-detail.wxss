/* group-detail.wxss */
.container {
  padding: 32rpx;
  background-color: #f6f6f6;
  min-height: 100vh;
}

/* 小组头部 */
.group-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 32rpx;
  padding: 48rpx;
  margin-bottom: 32rpx;
  color: white;
  box-shadow: 0 8rpx 40rpx rgba(102, 126, 234, 0.3);
}

.group-info {
  display: flex;
  align-items: center;
  margin-bottom: 32rpx;
}

.group-icon {
  width: 128rpx;
  height: 128rpx;
  border-radius: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 32rpx;
  font-size: 48rpx;
  background: rgba(255, 255, 255, 0.2);
}

.group-name {
  display: block;
  font-size: 40rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.group-meta {
  display: block;
  font-size: 24rpx;
  opacity: 0.8;
}

.group-description {
  display: block;
  font-size: 28rpx;
  line-height: 1.6;
  opacity: 0.9;
  margin-bottom: 32rpx;
}

.group-actions {
  display: flex;
  gap: 24rpx;
}

.action-btn {
  flex: 1;
  background: rgba(255, 255, 255, 0.2);
  border: none;
  border-radius: 24rpx;
  padding: 16rpx;
  color: white;
  font-size: 28rpx;
  font-weight: 500;
}

/* 卡片通用样式 */
.stats-card,
.members-card,
.activities-card {
  background: white;
  border-radius: 32rpx;
  padding: 40rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 8rpx 40rpx rgba(0, 0, 0, 0.08);
}

.card-title {
  display: block;
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 32rpx;
}

/* 统计卡片 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 32rpx;
  margin-bottom: 32rpx;
}

.stat-item {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #667eea;
  margin-bottom: 8rpx;
}

.stat-label {
  display: block;
  font-size: 24rpx;
  color: #666;
}

.progress-section {
  margin-bottom: 24rpx;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16rpx;
}

.progress-label {
  font-size: 28rpx;
  color: #666;
}

.progress-text {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
}

.progress-bar {
  width: 100%;
  height: 16rpx;
  background: #f0f0f0;
  border-radius: 8rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #4caf50 0%, #81c784 100%);
  border-radius: 8rpx;
  transition: width 0.3s ease;
}

/* 成员列表 */
.members-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32rpx;
}

.member-count {
  font-size: 28rpx;
  color: #666;
}

.members-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.member-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.member-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.member-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 24rpx;
}

.member-name-row {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}

.member-name {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-right: 16rpx;
}

.member-role {
  background: #fff3e0;
  color: #f57c00;
  font-size: 20rpx;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
}

.member-progress {
  font-size: 24rpx;
  color: #666;
}

.member-status {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
}

.member-status.completed {
  background: #e8f5e8;
  color: #4caf50;
}

.member-status.pending {
  background: #fff3e0;
  color: #ff9800;
}

.member-status.warning {
  background: #ffebee;
  color: #f44336;
}

.show-all-btn {
  width: 100%;
  background: #f5f5f5;
  color: #667eea;
  border: none;
  border-radius: 24rpx;
  padding: 24rpx;
  font-size: 28rpx;
  font-weight: 500;
  margin-top: 24rpx;
}

/* 最近动态 */
.activities-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.activity-item {
  display: flex;
  align-items: flex-start;
}

.activity-avatar {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  margin-right: 24rpx;
}

.activity-content {
  flex: 1;
}

.activity-text {
  display: block;
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
  margin-bottom: 8rpx;
}

.activity-time {
  font-size: 24rpx;
  color: #999;
}

/* 退出小组按钮 */
.leave-btn {
  width: 100%;
  background: #ffebee;
  color: #f44336;
  border: 2rpx solid #ffcdd2;
  border-radius: 24rpx;
  padding: 32rpx;
  font-size: 32rpx;
  font-weight: 600;
}
