// group-detail.ts

// Mock数据
const mockGroupData = {
  1: {
    id: 1,
    name: '晨跑俱乐部',
    icon: '🏃',
    iconColor: 'blue',
    memberCount: 32,
    role: '管理员',
    description: '每天早上6点一起晨跑，养成健康生活习惯，欢迎大家积极参与！',
    monthStats: {
      targetDays: 22,
      completedDays: 18,
      completionRate: 82
    },
    memberList: [
      {
        id: 1,
        name: '张小美',
        avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=80&h=80&fit=crop&crop=face',
        role: '管理员',
        currentCount: 22,
        targetCount: 22,
        status: 'completed',
        statusIcon: '✓'
      },
      {
        id: 2,
        name: '李小明',
        avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=80&h=80&fit=crop&crop=face',
        role: '成员',
        currentCount: 20,
        targetCount: 22,
        status: 'completed',
        statusIcon: '✓'
      },
      {
        id: 3,
        name: '王小红',
        avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=80&h=80&fit=crop&crop=face',
        role: '成员',
        currentCount: 18,
        targetCount: 22,
        status: 'pending',
        statusIcon: '⏰'
      },
      {
        id: 4,
        name: '刘小强',
        avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=80&h=80&fit=crop&crop=face',
        role: '成员',
        currentCount: 12,
        targetCount: 22,
        status: 'warning',
        statusIcon: '!'
      }
    ],
    recentActivities: [
      {
        id: 1,
        userAvatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=64&h=64&fit=crop&crop=face',
        text: '李小明完成了今日打卡',
        time: '2分钟前'
      },
      {
        id: 2,
        userAvatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=64&h=64&fit=crop&crop=face',
        text: '王小红加入了小组',
        time: '1小时前'
      },
      {
        id: 3,
        userAvatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=64&h=64&fit=crop&crop=face',
        text: '张小美创建了小组',
        time: '昨天'
      }
    ]
  }
}

Component({
  data: {
    groupId: '',
    groupInfo: {},
    monthStats: {},
    memberList: [],
    recentActivities: []
  },

  lifetimes: {
    attached() {
      // 获取页面参数
      const pages = getCurrentPages()
      const currentPage = pages[pages.length - 1]
      const options = currentPage.options
      
      if (options.id) {
        this.setData({
          groupId: options.id
        })
        this.loadGroupDetail(options.id)
      }
    }
  },

  methods: {
    // 加载小组详情
    loadGroupDetail(groupId: string) {
      const groupData = mockGroupData[groupId as keyof typeof mockGroupData]
      
      if (groupData) {
        this.setData({
          groupInfo: groupData,
          monthStats: groupData.monthStats,
          memberList: groupData.memberList.slice(0, 5), // 只显示前5个成员
          recentActivities: groupData.recentActivities
        })
      } else {
        wx.showToast({
          title: '小组不存在',
          icon: 'none'
        })
        setTimeout(() => {
          wx.navigateBack()
        }, 1500)
      }
    },

    // 邀请成员
    inviteMembers() {
      wx.showActionSheet({
        itemList: ['生成邀请链接', '生成二维码', '复制小组ID'],
        success: (res) => {
          if (res.tapIndex === 0) {
            this.generateInviteLink()
          } else if (res.tapIndex === 1) {
            this.generateQRCode()
          } else if (res.tapIndex === 2) {
            this.copyGroupId()
          }
        }
      })
    },

    // 生成邀请链接
    generateInviteLink() {
      const inviteLink = `小程序://打卡小程序/join-group?id=${this.data.groupId}`
      wx.setClipboardData({
        data: inviteLink,
        success: () => {
          wx.showToast({
            title: '邀请链接已复制',
            icon: 'success'
          })
        }
      })
    },

    // 生成二维码
    generateQRCode() {
      wx.showToast({
        title: '功能开发中',
        icon: 'none'
      })
    },

    // 复制小组ID
    copyGroupId() {
      wx.setClipboardData({
        data: this.data.groupId,
        success: () => {
          wx.showToast({
            title: '小组ID已复制',
            icon: 'success'
          })
        }
      })
    },

    // 管理小组
    manageGroup() {
      wx.showToast({
        title: '功能开发中',
        icon: 'none'
      })
    },

    // 查看全部成员
    showAllMembers() {
      const groupData = mockGroupData[this.data.groupId as keyof typeof mockGroupData]
      if (groupData) {
        this.setData({
          memberList: groupData.memberList
        })
      }
    },

    // 退出小组
    leaveGroup() {
      wx.showModal({
        title: '确认退出',
        content: '确定要退出这个小组吗？',
        success: (res) => {
          if (res.confirm) {
            wx.showToast({
              title: '已退出小组',
              icon: 'success'
            })
            
            setTimeout(() => {
              wx.navigateBack()
            }, 1500)
          }
        }
      })
    }
  }
})
