<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>微信小程序打卡系统 - UI设计展示</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background-color: #f6f6f6;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
            color: #333;
        }
        
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .header p {
            font-size: 1.1rem;
            color: #666;
            margin: 0;
        }
        
        .ui-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 30px;
            justify-items: center;
        }
        
        .ui-item {
            background-color: #f6f6f6;
            border-radius: 20px;
            padding: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .ui-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
        }
        
        .ui-item h3 {
            text-align: center;
            margin: 0 0 15px 0;
            color: #333;
            font-size: 1.2rem;
            font-weight: 600;
        }
        
        .ui-frame {
            width: 400px;
            height: 850px;
            border: none;
            border-radius: 15px;
            background-color: #f6f6f6;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }
        
        .stats {
            margin-top: 40px;
            text-align: center;
            padding: 30px;
            background: white;
            border-radius: 20px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
        }
        
        .stats h2 {
            color: #333;
            margin-bottom: 20px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .stat-item {
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            text-align: center;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .stat-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>微信小程序打卡系统</h1>
            <p>UI设计展示 - 基于清新主义美学的移动端界面设计</p>
        </div>
        
        <div class="ui-grid">
            <div class="ui-item">
                <h3>首页</h3>
                <iframe src="首页.html" class="ui-frame"></iframe>
            </div>
            
            <div class="ui-item">
                <h3>打卡页面</h3>
                <iframe src="打卡页面.html" class="ui-frame"></iframe>
            </div>
            
            <div class="ui-item">
                <h3>小组列表页面</h3>
                <iframe src="小组列表页面.html" class="ui-frame"></iframe>
            </div>
            
            <div class="ui-item">
                <h3>小组详情页面</h3>
                <iframe src="小组详情页面.html" class="ui-frame"></iframe>
            </div>
            
            <div class="ui-item">
                <h3>创建小组页面</h3>
                <iframe src="创建小组页面.html" class="ui-frame"></iframe>
            </div>
            
            <div class="ui-item">
                <h3>加入小组页面</h3>
                <iframe src="加入小组页面.html" class="ui-frame"></iframe>
            </div>
            
            <div class="ui-item">
                <h3>打卡记录页面</h3>
                <iframe src="打卡记录页面.html" class="ui-frame"></iframe>
            </div>
            
            <div class="ui-item">
                <h3>统计页面</h3>
                <iframe src="统计页面.html" class="ui-frame"></iframe>
            </div>
            
            <div class="ui-item">
                <h3>个人中心页面</h3>
                <iframe src="个人中心页面.html" class="ui-frame"></iframe>
            </div>
            
            <div class="ui-item">
                <h3>消息通知页面</h3>
                <iframe src="消息通知页面.html" class="ui-frame"></iframe>
            </div>
        </div>
        
        <div class="stats">
            <h2>设计统计</h2>
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-number">10</div>
                    <div class="stat-label">页面设计</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">375×812</div>
                    <div class="stat-label">设计尺寸</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">100%</div>
                    <div class="stat-label">响应式设计</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">清新主义</div>
                    <div class="stat-label">设计风格</div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
