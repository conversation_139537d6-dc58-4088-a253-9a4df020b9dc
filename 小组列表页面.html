<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>小组列表 - 打卡小程序</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body { overflow: hidden; }
        ::-webkit-scrollbar { display: none; }
        .gradient-bg { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .card-shadow { box-shadow: 0 4px 20px rgba(0,0,0,0.08); }
        .progress-bar { background: linear-gradient(90deg, #10b981 0%, #34d399 100%); }
    </style>
</head>
<body class="bg-gray-50">
    <!-- 手机模拟器外框 -->
    <div class="w-96 h-screen mx-auto bg-black rounded-3xl p-2 shadow-2xl">
        <div class="w-full h-full bg-white rounded-2xl overflow-hidden relative">
            <!-- 状态栏 -->
            <div class="flex justify-between items-center px-6 pt-3 pb-1 bg-transparent text-black text-sm font-medium">
                <span>9:41</span>
                <div class="flex items-center space-x-1">
                    <i class="fas fa-signal text-xs"></i>
                    <i class="fas fa-wifi text-xs"></i>
                    <i class="fas fa-battery-three-quarters text-xs"></i>
                </div>
            </div>

            <!-- 导航栏 -->
            <div class="flex items-center justify-between px-6 py-4 bg-transparent">
                <h1 class="text-xl font-bold text-gray-800">我的小组</h1>
                <button class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                    <i class="fas fa-plus text-white text-sm"></i>
                </button>
            </div>

            <!-- 主要内容区域 -->
            <div class="px-6 pb-20 h-full overflow-y-auto">
                <!-- 快速操作 -->
                <div class="flex space-x-3 mb-6">
                    <button class="flex-1 bg-blue-500 text-white py-3 rounded-xl font-medium flex items-center justify-center">
                        <i class="fas fa-plus mr-2"></i>创建小组
                    </button>
                    <button class="flex-1 bg-white border border-gray-200 text-gray-700 py-3 rounded-xl font-medium flex items-center justify-center card-shadow">
                        <i class="fas fa-qrcode mr-2"></i>加入小组
                    </button>
                </div>

                <!-- 小组列表 -->
                <div class="space-y-4">
                    <!-- 小组卡片1 -->
                    <div class="bg-white rounded-2xl p-5 card-shadow">
                        <div class="flex items-start justify-between mb-4">
                            <div class="flex items-center">
                                <div class="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center mr-4">
                                    <i class="fas fa-running text-blue-500 text-lg"></i>
                                </div>
                                <div>
                                    <h3 class="font-semibold text-gray-800 text-lg">晨跑俱乐部</h3>
                                    <p class="text-sm text-gray-500">32名成员 · 管理员</p>
                                </div>
                            </div>
                            <div class="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center">
                                <i class="fas fa-check text-green-500 text-xs"></i>
                            </div>
                        </div>
                        
                        <div class="mb-4">
                            <div class="flex justify-between text-sm mb-2">
                                <span class="text-gray-600">本月进度</span>
                                <span class="font-medium text-gray-800">18/22</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div class="progress-bar h-2 rounded-full" style="width: 82%"></div>
                            </div>
                        </div>

                        <div class="flex items-center justify-between">
                            <div class="flex -space-x-2">
                                <img src="https://images.unsplash.com/photo-1494790108755-2616b612b786?w=32&h=32&fit=crop&crop=face" class="w-8 h-8 rounded-full border-2 border-white">
                                <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=32&h=32&fit=crop&crop=face" class="w-8 h-8 rounded-full border-2 border-white">
                                <img src="https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=32&h=32&fit=crop&crop=face" class="w-8 h-8 rounded-full border-2 border-white">
                                <div class="w-8 h-8 bg-gray-200 rounded-full border-2 border-white flex items-center justify-center">
                                    <span class="text-xs text-gray-600">+29</span>
                                </div>
                            </div>
                            <button class="text-blue-500 text-sm font-medium">查看详情</button>
                        </div>
                    </div>

                    <!-- 小组卡片2 -->
                    <div class="bg-white rounded-2xl p-5 card-shadow">
                        <div class="flex items-start justify-between mb-4">
                            <div class="flex items-center">
                                <div class="w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center mr-4">
                                    <i class="fas fa-book text-purple-500 text-lg"></i>
                                </div>
                                <div>
                                    <h3 class="font-semibold text-gray-800 text-lg">读书分享会</h3>
                                    <p class="text-sm text-gray-500">18名成员 · 普通成员</p>
                                </div>
                            </div>
                            <div class="w-6 h-6 bg-yellow-100 rounded-full flex items-center justify-center">
                                <i class="fas fa-clock text-yellow-500 text-xs"></i>
                            </div>
                        </div>
                        
                        <div class="mb-4">
                            <div class="flex justify-between text-sm mb-2">
                                <span class="text-gray-600">本月进度</span>
                                <span class="font-medium text-gray-800">15/20</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div class="bg-yellow-400 h-2 rounded-full" style="width: 75%"></div>
                            </div>
                        </div>

                        <div class="flex items-center justify-between">
                            <div class="flex -space-x-2">
                                <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=32&h=32&fit=crop&crop=face" class="w-8 h-8 rounded-full border-2 border-white">
                                <img src="https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=32&h=32&fit=crop&crop=face" class="w-8 h-8 rounded-full border-2 border-white">
                                <img src="https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?w=32&h=32&fit=crop&crop=face" class="w-8 h-8 rounded-full border-2 border-white">
                                <div class="w-8 h-8 bg-gray-200 rounded-full border-2 border-white flex items-center justify-center">
                                    <span class="text-xs text-gray-600">+15</span>
                                </div>
                            </div>
                            <button class="text-blue-500 text-sm font-medium">查看详情</button>
                        </div>
                    </div>

                    <!-- 小组卡片3 -->
                    <div class="bg-white rounded-2xl p-5 card-shadow">
                        <div class="flex items-start justify-between mb-4">
                            <div class="flex items-center">
                                <div class="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center mr-4">
                                    <i class="fas fa-dumbbell text-green-500 text-lg"></i>
                                </div>
                                <div>
                                    <h3 class="font-semibold text-gray-800 text-lg">健身打卡</h3>
                                    <p class="text-sm text-gray-500">45名成员 · 普通成员</p>
                                </div>
                            </div>
                            <div class="w-6 h-6 bg-red-100 rounded-full flex items-center justify-center">
                                <i class="fas fa-exclamation text-red-500 text-xs"></i>
                            </div>
                        </div>
                        
                        <div class="mb-4">
                            <div class="flex justify-between text-sm mb-2">
                                <span class="text-gray-600">本月进度</span>
                                <span class="font-medium text-red-500">12/25</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div class="bg-red-400 h-2 rounded-full" style="width: 48%"></div>
                            </div>
                        </div>

                        <div class="flex items-center justify-between">
                            <div class="flex -space-x-2">
                                <img src="https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=32&h=32&fit=crop&crop=face" class="w-8 h-8 rounded-full border-2 border-white">
                                <img src="https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=32&h=32&fit=crop&crop=face" class="w-8 h-8 rounded-full border-2 border-white">
                                <img src="https://images.unsplash.com/photo-1517841905240-472988babdf9?w=32&h=32&fit=crop&crop=face" class="w-8 h-8 rounded-full border-2 border-white">
                                <div class="w-8 h-8 bg-gray-200 rounded-full border-2 border-white flex items-center justify-center">
                                    <span class="text-xs text-gray-600">+42</span>
                                </div>
                            </div>
                            <button class="text-blue-500 text-sm font-medium">查看详情</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 底部导航栏 -->
            <div class="absolute bottom-0 left-0 right-0 bg-white border-t border-gray-100 px-6 py-3">
                <div class="flex justify-around items-center">
                    <div class="flex flex-col items-center">
                        <i class="fas fa-home text-gray-400 text-lg mb-1"></i>
                        <span class="text-xs text-gray-400">首页</span>
                    </div>
                    <div class="flex flex-col items-center">
                        <i class="fas fa-users text-blue-500 text-lg mb-1"></i>
                        <span class="text-xs text-blue-500 font-medium">小组</span>
                    </div>
                    <div class="flex flex-col items-center">
                        <i class="fas fa-chart-bar text-gray-400 text-lg mb-1"></i>
                        <span class="text-xs text-gray-400">统计</span>
                    </div>
                    <div class="flex flex-col items-center">
                        <i class="fas fa-user text-gray-400 text-lg mb-1"></i>
                        <span class="text-xs text-gray-400">我的</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
