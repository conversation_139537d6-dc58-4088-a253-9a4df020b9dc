<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>打卡记录 - 打卡小程序</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body { overflow: hidden; }
        ::-webkit-scrollbar { display: none; }
        .gradient-bg { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .card-shadow { box-shadow: 0 4px 20px rgba(0,0,0,0.08); }
        .calendar-day { width: 40px; height: 40px; }
        .checked-day { background: linear-gradient(135deg, #10b981, #34d399); }
    </style>
</head>
<body class="bg-gray-50">
    <!-- 手机模拟器外框 -->
    <div class="mx-auto bg-black rounded-3xl p-2 shadow-2xl" style="width: 375px; height: 812px;">
        <div class="w-full h-full bg-white rounded-2xl overflow-hidden relative">
            <!-- 状态栏 -->
            <div class="flex justify-between items-center px-6 pt-3 pb-1 bg-transparent text-black text-sm font-medium">
                <span>9:41</span>
                <div class="flex items-center space-x-1">
                    <i class="fas fa-signal text-xs"></i>
                    <i class="fas fa-wifi text-xs"></i>
                    <i class="fas fa-battery-three-quarters text-xs"></i>
                </div>
            </div>

            <!-- 导航栏 -->
            <div class="flex items-center justify-between px-6 py-4 bg-transparent">
                <button class="w-8 h-8 flex items-center justify-center">
                    <i class="fas fa-arrow-left text-gray-600"></i>
                </button>
                <h1 class="text-lg font-semibold text-gray-800">打卡记录</h1>
                <button class="w-8 h-8 flex items-center justify-center">
                    <i class="fas fa-calendar-alt text-gray-600"></i>
                </button>
            </div>

            <!-- 主要内容区域 -->
            <div class="px-6 pb-20 h-full overflow-y-auto">
                <!-- 月份导航 -->
                <div class="flex items-center justify-between mb-6">
                    <button class="w-8 h-8 flex items-center justify-center">
                        <i class="fas fa-chevron-left text-gray-600"></i>
                    </button>
                    <h2 class="text-xl font-bold text-gray-800">2024年6月</h2>
                    <button class="w-8 h-8 flex items-center justify-center">
                        <i class="fas fa-chevron-right text-gray-600"></i>
                    </button>
                </div>

                <!-- 日历视图 -->
                <div class="bg-white rounded-2xl p-5 mb-6 card-shadow">
                    <!-- 星期标题 -->
                    <div class="grid grid-cols-7 gap-1 mb-3">
                        <div class="text-center text-sm text-gray-500 py-2">日</div>
                        <div class="text-center text-sm text-gray-500 py-2">一</div>
                        <div class="text-center text-sm text-gray-500 py-2">二</div>
                        <div class="text-center text-sm text-gray-500 py-2">三</div>
                        <div class="text-center text-sm text-gray-500 py-2">四</div>
                        <div class="text-center text-sm text-gray-500 py-2">五</div>
                        <div class="text-center text-sm text-gray-500 py-2">六</div>
                    </div>
                    
                    <!-- 日期网格 -->
                    <div class="grid grid-cols-7 gap-1">
                        <!-- 空白日期 -->
                        <div class="calendar-day"></div>
                        <div class="calendar-day"></div>
                        <div class="calendar-day"></div>
                        <div class="calendar-day"></div>
                        <div class="calendar-day"></div>
                        <div class="calendar-day"></div>
                        
                        <!-- 6月日期 -->
                        <div class="calendar-day checked-day text-white rounded-lg flex items-center justify-center text-sm font-medium">1</div>
                        <div class="calendar-day bg-gray-100 rounded-lg flex items-center justify-center text-sm text-gray-400">2</div>
                        <div class="calendar-day checked-day text-white rounded-lg flex items-center justify-center text-sm font-medium">3</div>
                        <div class="calendar-day checked-day text-white rounded-lg flex items-center justify-center text-sm font-medium">4</div>
                        <div class="calendar-day checked-day text-white rounded-lg flex items-center justify-center text-sm font-medium">5</div>
                        <div class="calendar-day checked-day text-white rounded-lg flex items-center justify-center text-sm font-medium">6</div>
                        <div class="calendar-day checked-day text-white rounded-lg flex items-center justify-center text-sm font-medium">7</div>
                        
                        <div class="calendar-day bg-gray-100 rounded-lg flex items-center justify-center text-sm text-gray-400">8</div>
                        <div class="calendar-day bg-gray-100 rounded-lg flex items-center justify-center text-sm text-gray-400">9</div>
                        <div class="calendar-day checked-day text-white rounded-lg flex items-center justify-center text-sm font-medium">10</div>
                        <div class="calendar-day checked-day text-white rounded-lg flex items-center justify-center text-sm font-medium">11</div>
                        <div class="calendar-day checked-day text-white rounded-lg flex items-center justify-center text-sm font-medium">12</div>
                        <div class="calendar-day checked-day text-white rounded-lg flex items-center justify-center text-sm font-medium">13</div>
                        <div class="calendar-day checked-day text-white rounded-lg flex items-center justify-center text-sm font-medium">14</div>
                        
                        <div class="calendar-day bg-gray-100 rounded-lg flex items-center justify-center text-sm text-gray-400">15</div>
                        <div class="calendar-day bg-gray-100 rounded-lg flex items-center justify-center text-sm text-gray-400">16</div>
                        <div class="calendar-day checked-day text-white rounded-lg flex items-center justify-center text-sm font-medium">17</div>
                        <div class="calendar-day checked-day text-white rounded-lg flex items-center justify-center text-sm font-medium">18</div>
                        <div class="calendar-day checked-day text-white rounded-lg flex items-center justify-center text-sm font-medium">19</div>
                        <div class="calendar-day checked-day text-white rounded-lg flex items-center justify-center text-sm font-medium">20</div>
                        <div class="calendar-day checked-day text-white rounded-lg flex items-center justify-center text-sm font-medium">21</div>
                        
                        <div class="calendar-day bg-gray-100 rounded-lg flex items-center justify-center text-sm text-gray-400">22</div>
                        <div class="calendar-day bg-gray-100 rounded-lg flex items-center justify-center text-sm text-gray-400">23</div>
                        <div class="calendar-day checked-day text-white rounded-lg flex items-center justify-center text-sm font-medium">24</div>
                        <div class="calendar-day checked-day text-white rounded-lg flex items-center justify-center text-sm font-medium">25</div>
                        <div class="calendar-day checked-day text-white rounded-lg flex items-center justify-center text-sm font-medium">26</div>
                        <div class="calendar-day checked-day text-white rounded-lg flex items-center justify-center text-sm font-medium">27</div>
                        <div class="calendar-day bg-blue-500 text-white rounded-lg flex items-center justify-center text-sm font-medium border-2 border-blue-600">28</div>
                        
                        <div class="calendar-day bg-gray-100 rounded-lg flex items-center justify-center text-sm text-gray-400">29</div>
                        <div class="calendar-day bg-gray-100 rounded-lg flex items-center justify-center text-sm text-gray-400">30</div>
                    </div>
                </div>

                <!-- 本月统计 -->
                <div class="bg-white rounded-2xl p-5 mb-6 card-shadow">
                    <h3 class="font-semibold text-gray-800 mb-4">本月统计</h3>
                    <div class="grid grid-cols-3 gap-4">
                        <div class="text-center">
                            <div class="text-2xl font-bold text-green-500 mb-1">18</div>
                            <div class="text-sm text-gray-500">已打卡</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-red-500 mb-1">10</div>
                            <div class="text-sm text-gray-500">未打卡</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-blue-500 mb-1">82%</div>
                            <div class="text-sm text-gray-500">完成率</div>
                        </div>
                    </div>
                </div>

                <!-- 今日打卡详情 -->
                <div class="bg-white rounded-2xl p-5 card-shadow">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="font-semibold text-gray-800">6月28日 打卡详情</h3>
                        <span class="bg-green-100 text-green-600 text-xs px-2 py-1 rounded-full">已完成</span>
                    </div>
                    
                    <div class="mb-4">
                        <img src="https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=300&h=200&fit=crop" class="w-full h-32 object-cover rounded-xl">
                    </div>
                    
                    <div class="mb-4">
                        <p class="text-gray-700 text-sm leading-relaxed">今天的晨跑特别棒！天气很好，跑了5公里，感觉身体状态越来越好了。坚持就是胜利！💪</p>
                    </div>
                    
                    <div class="flex items-center justify-between text-sm text-gray-500">
                        <div class="flex items-center">
                            <i class="fas fa-clock mr-2"></i>
                            <span>06:30</span>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-map-marker-alt mr-2"></i>
                            <span>中央公园</span>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-running mr-2"></i>
                            <span>晨跑俱乐部</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 底部导航栏 -->
            <div class="absolute bottom-0 left-0 right-0 bg-white border-t border-gray-100 px-6 py-3">
                <div class="flex justify-around items-center">
                    <div class="flex flex-col items-center">
                        <i class="fas fa-home text-gray-400 text-lg mb-1"></i>
                        <span class="text-xs text-gray-400">首页</span>
                    </div>
                    <div class="flex flex-col items-center">
                        <i class="fas fa-users text-gray-400 text-lg mb-1"></i>
                        <span class="text-xs text-gray-400">小组</span>
                    </div>
                    <div class="flex flex-col items-center">
                        <i class="fas fa-chart-bar text-blue-500 text-lg mb-1"></i>
                        <span class="text-xs text-blue-500 font-medium">统计</span>
                    </div>
                    <div class="flex flex-col items-center">
                        <i class="fas fa-user text-gray-400 text-lg mb-1"></i>
                        <span class="text-xs text-gray-400">我的</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
